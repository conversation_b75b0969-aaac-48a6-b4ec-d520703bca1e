import 'package:flutter/material.dart';
import '../../domain/entities/assigned_course_entity.dart';

/// Dialog for displaying course details with conditional button actions
class CourseDetailDialog extends StatelessWidget {
  final AssignedCourseEntity course;

  const CourseDetailDialog({
    super.key,
    required this.course,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      // insetPadding: EdgeInsets.all(10),
      child: Container(
        height: 400,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.5),
              offset: const Offset(0, 6),
              blurRadius: 6,
            ),
          ],
        ),
        child: Column(
          children: [
            // Course info section
            Padding(
              padding: const EdgeInsets.fromLTRB(15, 10, 15, 0),
              child: Row(
                children: [
                  // Course avatar
                  Container(
                    width: 45,
                    height: 41,
                    decoration: BoxDecoration(
                      color: const Color(0xFFD16D6A),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.black, width: 1),
                    ),
                    child: Center(
                      child: Text(
                        _getCourseInitials(course.cours),
                        style: const TextStyle(
                          fontWeight: FontWeight.w400,
                          fontSize: 17,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Course name
                  Expanded(
                    child: Text(
                      course.cours,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        letterSpacing: 0.25,
                        color: Color(0xFF0D0D0D),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),
            // Course details
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Volume horaire : ${course.volumeHoraireConsomme} Heures / ${course.volumeHoraire}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 13,
                      letterSpacing: 0.25,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    'Taux : ${course.tauxOuForfait}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 13,
                      letterSpacing: 0.25,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(),
            // Action buttons section
            _buildActionButtons(context),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// Build action buttons based on document availability
  Widget _buildActionButtons(BuildContext context) {
    if (!course.hasDocument) {
      // Show import syllabus button
      return _buildImportSyllabusButton(context);
    } else {
      // Show download and delete buttons
      return _buildDownloadDeleteButtons(context);
    }
  }

  /// Build import syllabus button
  Widget _buildImportSyllabusButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15),
      width: double.infinity,
      height: 35,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.black.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.5),
            offset: const Offset(0, 6),
            blurRadius: 6,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _handleImportSyllabus(context),
          borderRadius: BorderRadius.circular(10),
          child: const Row(
            children: [
              SizedBox(width: 15),
              Icon(Icons.cloud_upload_outlined, size: 22, color: Colors.black),
              SizedBox(width: 10),
              Text(
                'Importer un syllabus',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 13,
                  letterSpacing: 0.25,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build download and delete buttons
  Widget _buildDownloadDeleteButtons(BuildContext context) {
    return Column(
      children: [
        // Document info
        if (course.nomDocument != null)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: Row(
              children: [
                const Icon(Icons.description, size: 26, color: Colors.grey),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    course.nomDocument!,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 13,
                      letterSpacing: 0.25,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
        const SizedBox(height: 15),
        // Action buttons
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: Column(
            children: [
              // Download button
              _buildActionButton(
                context: context,
                icon: Icons.download,
                text: 'Télécharger le fichier',
                onTap: () => _handleDownload(context),
              ),
              const SizedBox(height: 10),
              // Delete button
              _buildActionButton(
                context: context,
                icon: Icons.delete,
                text: 'Supprimer le fichier',
                onTap: () => _handleDelete(context),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build individual action button
  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String text,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      height: 35,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.black.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.5),
            offset: const Offset(0, 6),
            blurRadius: 6,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(10),
          child: Row(
            children: [
              const SizedBox(width: 15),
              Icon(icon, size: 22, color: Colors.black),
              const SizedBox(width: 10),
              Text(
                text,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 13,
                  letterSpacing: 0.25,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Generate course initials from course name
  String _getCourseInitials(String courseName) {
    final words = courseName.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else if (words.isNotEmpty) {
      return words[0].length >= 2 
          ? words[0].substring(0, 2).toUpperCase()
          : words[0][0].toUpperCase();
    }
    return 'C';
  }

  /// Handle import syllabus action
  void _handleImportSyllabus(BuildContext context) {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Fonctionnalité d\'import de syllabus en cours de développement'),
      ),
    );
  }

  /// Handle download action
  void _handleDownload(BuildContext context) {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Téléchargement du fichier en cours...'),
      ),
    );
  }

  /// Handle delete action
  void _handleDelete(BuildContext context) {
    Navigator.of(context).pop();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Suppression du fichier en cours...'),
      ),
    );
  }
}
