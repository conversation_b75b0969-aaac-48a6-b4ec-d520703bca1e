import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/payment_request_entity.dart';
import '../entities/payment_response_entity.dart';
import '../entities/professor_payment_entity.dart';

/// Abstract repository interface for payment operations
abstract class PaymentRepository {
  /// Process a payment request
  ///
  /// Returns [PaymentResponseEntity] on success or [Failure] on error
  Future<Either<Failure, PaymentResponseEntity>> processPayment(
    PaymentRequestEntity paymentRequest,
  );

  /// Get list of professor payments
  ///
  /// Returns [List<ProfessorPaymentEntity>] on success or [Failure] on error
  Future<Either<Failure, List<ProfessorPaymentEntity>>> getProfessorPayments({
    required String codeEtab,
    required String telephone,
    required String codeUtilisateur,
  });
}
