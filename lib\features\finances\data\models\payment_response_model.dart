/// Model for payment response data from API
class PaymentResponseModel {
  final String paymentUrl;
  final String transactionId;
  final String qrCode;
  final String qrId;

  PaymentResponseModel({
    required this.paymentUrl,
    required this.transactionId,
    required this.qrCode,
    required this.qrId,
  });

  /// Create model from JSON response
  factory PaymentResponseModel.fromJson(Map<String, dynamic> json) {
    return PaymentResponseModel(
      paymentUrl: json['paymentUrl'] as String,
      transactionId: json['transactionId'] as String,
      qrCode: json['qrCode'] as String,
      qrId: json['qrId'] as String,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'paymentUrl': paymentUrl,
      'transactionId': transactionId,
      'qrCode': qrCode,
      'qrId': qrId,
    };
  }
}
