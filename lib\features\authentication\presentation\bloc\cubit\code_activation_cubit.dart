import 'package:flutter/material.dart';
import 'package:kairos/core/error/failures.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/verify_pin_usecase.dart'; // Import the use case
import '../../../domain/usecases/save_fullname_usecase.dart'; // Import the new use case
import '../state/auth_state.dart';

/// Cubit for managing code activation state
class CodeActivationCubit extends Cubit<AuthState> {
  final VerifyPinUseCase verifyPinUseCase; // Add use case dependency
  final SaveFullNameUseCase saveFullNameUseCase; // Add new use case dependency

  CodeActivationCubit({
    required this.verifyPinUseCase,
    required this.saveFullNameUseCase,
  }) : super(const AuthInitial()); // Update constructor

  /// Verify the activation PIN
  ///
  /// [otp] - The entered OTP/PIN
  /// [phoneNumber] - The user's phone number
  Future<void> verifyPin(String otp, String phoneNumber) async {
    emit(const CodeActivationLoadingState());

    final result = await verifyPinUseCase(otp, phoneNumber); // Call the use case

    result.fold(
      (failure) {
        // Handle failure
        if (failure is ServerFailure) {
          // Access returnCode directly from ServerFailure
          debugPrint('CodeActivationCubit: ServerFailure: ${failure.returnCode}');
          debugPrint('CodeActivationCubit: ServerFailure: ${failure.message}');
          emit(CodeActivationErrorState(
            returnCode: failure.returnCode,
            userMessage: failure.message, // message is the userMessage
          ));
        } else {
          // Handle other failure types
          debugPrint('CodeActivationCubit: Other failure type: ${failure.runtimeType}');
          emit(CodeActivationErrorState(userMessage: failure.message));
        }
      },
      (success) {
        // Handle success
        emit(const CodeActivationSuccessState());
      },
    );
  }

  /// Save the user's full name
  ///
  /// [fullName] - The user's full name to save
  Future<void> saveFullName(String fullName) async {
    emit(const CodeActivationLoadingState());

    final result = await saveFullNameUseCase(fullName); // Call the use case

    result.fold(
      (failure) {
        // Handle failure
        if (failure is ServerFailure) {
          // Access returnCode directly from ServerFailure
          emit(CodeActivationErrorState(
            returnCode: failure.returnCode,
            userMessage: failure.message, // message is the userMessage
          ));
        } else {
          // Handle other failure types
          emit(CodeActivationErrorState(userMessage: failure.message));
        }
      },
      (success) {
        // Handle success
        emit(const CodeActivationSuccessState());
      },
    );
  }
}