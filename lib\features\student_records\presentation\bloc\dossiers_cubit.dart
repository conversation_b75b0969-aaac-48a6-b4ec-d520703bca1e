import 'dart:convert';
import 'dart:io';
import 'package:kairos/core/error/failures.dart';
import 'package:kairos/features/student_records/domain/usecases/get_dossiers.dart';
import 'package:kairos/features/student_records/domain/usecases/get_dossiers_by_year.dart'; // Import the new use case
import 'package:kairos/features/student_records/domain/usecases/get_dossier_attachment.dart';
import "package:flutter_bloc/flutter_bloc.dart";
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';
import 'dossiers_state.dart';
import 'package:flutter/material.dart';
import 'package:kairos/features/student_records/domain/entities/dossier_entity.dart'; // Import DossierEntity


class DossiersCubit extends Cubit<DossiersState> {
  final GetDossiersUseCase getDossiersUseCase;
  final GetDossiersByYearUseCase getDossiersByYearUseCase;
  final GetDossierAttachmentUseCase getDossierAttachmentUseCase;

  // Store the currently loaded dossiers to preserve them during attachment operations
  List<DossierEntity> _currentDossiers = [];

  DossiersCubit({
    required this.getDossiersUseCase,
    required this.getDossiersByYearUseCase,
    required this.getDossierAttachmentUseCase,
  }) : super(DossiersInitial());

  Future<void> fetchDossiers({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(DossiersLoading());
    final failureOrDossiers = await getDossiersUseCase(
      GetDossiersParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrDossiers.fold(
      (failure) {
        if (failure is ServerFailure) {
          emit(DossiersError('Erreur lors de la récupération des dossiers: Veuillez réessayer plus tard.'));
        } else if (failure is NetworkFailure) {
          emit(DossiersError('Erreur de connexion lors de la récupération des dossiers: Veuillez vérifier votre connexion internet.'));
        } else {
          emit(DossiersError('Erreur inattendue lors de la récupération des dossiers: Veuillez réessayer plus tard.'));
        }
      },
      (dossiers) {
        _currentDossiers = dossiers; // Update the internal list
        emit(DossiersLoaded(_currentDossiers, attachmentStatus: AttachmentStatus.initial));
      },
    );
  }

  /// Fetches student dossiers filtered by year.
  /// This method handles the state management for fetching dossiers based on the academic year,
  /// emitting loading, error, or loaded states as appropriate.
  Future<void> fetchDossiersByYear({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String annee,
    String? codeUtilisateur,
  }) async {
    emit(DossiersLoading());
    final failureOrDossiers = await getDossiersByYearUseCase(
      GetDossiersByYearParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        annee: annee,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrDossiers.fold(
      (failure) {
        if (failure is ServerFailure) {
          emit(DossiersError('Erreur lors de la récupération des dossiers par année: Veuillez réessayer plus tard.'));
        } else if (failure is NetworkFailure) {
          emit(DossiersError('Erreur de connexion lors de la récupération des dossiers par année: Veuillez vérifier votre connexion internet.'));
        } else {
          emit(DossiersError('Erreur inattendue lors de la récupération des dossiers par année: Veuillez réessayer plus tard.'));
        }
      },
      (dossiers) {
        _currentDossiers = dossiers; // Update the internal list
        emit(DossiersLoaded(_currentDossiers, attachmentStatus: AttachmentStatus.initial));
      },
    );
  }

  /// Fetches and opens a dossier attachment file.
  /// This method handles the complete flow from fetching the base64 content
  /// to saving it locally and opening it with the system default application.
  Future<void> fetchDossierAttachment({
    required String codeEtab,
    required String numeroTel,
    required String codeEtudiant,
    required int idObject,
    String? codeUtilisateur,
    String? fileName,
  }) async {
    // Emit loading state for attachment while preserving the main dossier list
    emit(DossiersLoaded(_currentDossiers, attachmentStatus: AttachmentStatus.loading));

    final failureOrAttachment = await getDossierAttachmentUseCase(
      GetDossierAttachmentParams(
        codeEtab: codeEtab,
        numeroTel: numeroTel,
        codeEtudiant: codeEtudiant,
        idObject: idObject,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrAttachment.fold(
      (failure) {
        String errorMessage;
        if (failure is ServerFailure) {
          errorMessage = 'Erreur lors du téléchargement du fichier. Veuillez réessayer plus tard.';
        } else if (failure is NetworkFailure) {
          errorMessage = 'Erreur de connexion lors du téléchargement du fichier. Veuillez vérifier votre connexion internet.';
        } else {
          errorMessage = 'Erreur inattendue lors du téléchargement du fichier. Veuillez réessayer plus tard.';
        }
        // Emit error state for attachment while preserving the main dossier list
        emit(DossiersLoaded(_currentDossiers, attachmentStatus: AttachmentStatus.error, attachmentErrorMessage: errorMessage));
      },
      (attachment) async {
        try {
          // Check if the API returned success
          if (attachment.returnCode != 'SUCCESS') {
            // Emit error state for attachment while preserving the main dossier list
            emit(DossiersLoaded(_currentDossiers, attachmentStatus: AttachmentStatus.error, attachmentErrorMessage: 'Erreur lors du téléchargement du fichier. Veuillez réessayer plus tard.'));
            return;
          }

          // Decode base64 content
          final bytes = base64Decode(attachment.file);

          // Get app documents directory
          final directory = await getApplicationDocumentsDirectory();

          // Generate filename based on type and current timestamp
          final extension = _getFileExtension(attachment.type);
          final timestamp = DateTime.now().millisecondsSinceEpoch;
          debugPrint('DossiersCubit: Fetching attachment with params:');
          debugPrint('  codeEtab: $codeEtab');
          debugPrint('  numeroTel: $numeroTel');
          debugPrint('  codeEtudiant: $codeEtudiant');
          debugPrint('  extension: $extension');
          debugPrint('  codeUtilisateur: $codeUtilisateur');
          debugPrint('  fileName: $fileName');
          final finalFileName = fileName ?? 'dossier_attachment_$timestamp$extension';

          // Create file path
          final filePath = '${directory.path}/$finalFileName';

          // Write file to storage
          final file = File(filePath);
          await file.writeAsBytes(bytes);

          // Emit success state for attachment while preserving the main dossier list
          emit(DossiersLoaded(_currentDossiers, attachmentStatus: AttachmentStatus.loaded));

          // Open file with system default application
          final result = await OpenFile.open(filePath, type: attachment.type);
          if (result.type != ResultType.done) {
            // Emit error state for attachment while preserving the main dossier list
            emit(DossiersLoaded(_currentDossiers, attachmentStatus: AttachmentStatus.error, attachmentErrorMessage: 'Failed to open file: ${result.message}'));
          }
        } catch (e) {
          // Emit error state for attachment while preserving the main dossier list
          emit(DossiersLoaded(_currentDossiers, attachmentStatus: AttachmentStatus.error, attachmentErrorMessage: 'Failed to process attachment: $e'));
        }
      },
    );
  }

  /// Helper method to get file extension from MIME type
  String _getFileExtension(String mimeType) {
    switch (mimeType.toLowerCase()) {
      case 'application/pdf':
        return '.pdf';
      case 'image/jpeg':
      case 'image/jpg':
        return '.jpg';
      case 'image/png':
        return '.png';
      case 'image/gif':
        return '.gif';
      case 'application/msword':
        return '.doc';
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return '.docx';
      case 'application/vnd.ms-excel':
        return '.xls';
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return '.xlsx';
      case 'text/plain':
        return '.txt';
      default:
        return '.bin'; // Generic binary file extension
    }
  }
}