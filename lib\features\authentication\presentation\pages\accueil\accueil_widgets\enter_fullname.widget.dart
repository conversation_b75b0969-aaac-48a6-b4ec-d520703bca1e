import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/features/authentication/presentation/bloc/cubit/code_activation_cubit.dart';
import 'package:kairos/features/authentication/presentation/bloc/state/auth_state.dart';
import 'package:kairos/core/widgets/common/snackbar_widget.dart';

class EnterFullnameWidget extends StatefulWidget{
  const EnterFullnameWidget({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<EnterFullnameWidget> createState() => _EnterFullnameWidgetState();
}

class _EnterFullnameWidgetState extends State<EnterFullnameWidget>{

  final TextEditingController _fullnameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void dispose(){
    super.dispose();
    _fullnameController.dispose();
  }

  @override
  Widget build(BuildContext context){
    return BlocListener<CodeActivationCubit, AuthState>(
      listener: (context, state) {
        if (state is CodeActivationLoadingState) {
          setState(() {
            _isLoading = true;
          });
        } else if (state is CodeActivationSuccessState) {
          setState(() {
            _isLoading = false;
          });
          // Navigate to the next page (AppActivatedWidget) on success
          widget.pageController.nextPage(
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        } else if (state is CodeActivationErrorState) {
          setState(() {
            _isLoading = false;
          });
          debugPrint('CodeActivationErrorState: ${state.userMessage}');
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: state.userMessage,
              isError: true,
            ).getSnackBar(),
          );
          // Navigate back to CodeActivationWidget on error
          // The error message will be displayed by the listener in CodeActivationWidget
          widget.pageController.previousPage(
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Text("ENTRER VOTRE NOM COMPLET", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
          SvgPicture.asset("assets/images/logo_kairos.svg"),
          SizedBox(height: 20,
                   width: 200,
                   child: Divider(color: Theme.of(context).primaryColor, thickness: 4),),
          Spacer(),
          Flexible(flex: 8, child: SvgPicture.asset("assets/images/illustration_profil.svg")),
          // Spacer(flex: 2),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Text("Veuillez saisir votre nom complet.", textAlign: TextAlign.center),
          ),
          Spacer(flex: 3),
          Form(
            key: _formKey,
            child: SizedBox(
              width: 300,
              child: TextFormField(
                controller: _fullnameController,
                decoration: InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: "Nom complet",
                  hintStyle: TextStyle(color: Colors.grey.shade400),
                  contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  ),
                keyboardType: TextInputType.name,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[^0-9]')),
                ],
                validator: (value){
                  if(value!.isEmpty){
                    return "Veuillez saisir votre nom complet";
                  } else {
                    return null;
                  }
                },
              ),

            ),
          ),
          Spacer(),
          FilledButton(
            style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                fixedSize: WidgetStateProperty.all(Size(300, 50))),
            onPressed: _isLoading ? null : () async {
              debugPrint('the user clicked on `Continue` button');
              if(_formKey.currentState!.validate()){
                setState(() {
                  _isLoading = true;
                });

                // Call saveFullName method in CodeActivationCubit
                debugPrint('EnterFullnameWidget: Calling saveFullName with fullName: ${_fullnameController.text.trim()}');
                context.read<CodeActivationCubit>().saveFullName(_fullnameController.text.trim());
              }
            },
            child: _isLoading
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : const Text("CONTINUER", style: TextStyle(fontWeight: FontWeight.bold)),
            ),
          Spacer(flex: 3),
        ],
      ),
    );
  }
}