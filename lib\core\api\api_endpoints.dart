/// API endpoint constants
class ApiEndpoints {
  // Base paths
  static const String auth = '/auth';
  static const String user = '/user';

  static const String school = "/etablissement";
  static const String comm = '/communication';
  
  // Authentication endpoints
  static const String verifyPin = '$comm/verification-pin'; // corrected from verify-pin
  static const String saveFullName = '$comm/verification-nom-complet'; // corrected from verify-pin
  static const String sendSms = '$user/send-sms';
  static const String resendSms = '$user/renvoi-pin-activation';
  static const String refreshToken = '/token-manager/refresh-token';
  static const String forgotPassword = '$school/send-pin-forget-password';
  static const String resetPassword = '$school/forget-password';
  static const String payment = '$school/process-init-payment';
  static const String verifyPaymentStatus= '$school/payment-status';
  
  // School endpoints
  static const String studentSchoolList = '$school/check-liste-etablissements';
  static const String activateSchool = '$school/login';
  static const String allAvailableSchools = school;
  static const String suppressionEtablissement = '$school/deconnexion';
  static const String suppressionCompte = '$school/supression-compte';

  // User endpoints
  static const String profile = '$user/profile';
  static const String updateProfile = '$user/profile/update';
  static const String checkResponse = '$user/check-response';
  static const String ressourcesPedagogiques = '/ressources-pedagogiques';
  

  static const String getEnfantsDuTuteur = '/enfants-parent';
  static const String statutFinancierEtudiantTuteur = '/statut-financier-etudiant';
  static const String dashboard = '/dashboard';
  static const String notificationsEtudiant = '/notifications';
  static const String notesEvaluations = '/notes-evaluations';
  static const String notesEvaluationsFiltres = '/notes-evaluations-filtres';
  static const String absencesRetards = '/absences-retards';
  static const String absencesRetardsFiltres = '/absences-retards-filtres';
  static const String emploisDuTemps = '/emplois-du-temps/etudiant';
  static const String emploisDuTempsFiltres = '/emplois-du-temps-filtres';
  static const String documentsEtudiant = '/documents-etudiant';
  static const String documentsEtudiantAnnee = '/documents-etudiant-annee';
  static const String dossierEtudiantConsultation = '/dossier-etudiant-consultation';
  static const String cahierTexte = '/cahier-texte-classe';
  static const String saisiCahierTexte = '/saisie-cahier-texte-responsable';
  static const String cahierTexteConsultation = '/cahier-texte-consultation';
  static const String cahierTexteAnnuel = '/cahier-texte-annuel';
  static const String getFraisPayes = '/frais-payes';
  static const String fraisPayesFiltres = '/frais-payes-filtres';
  static const String fraisImpayes = '/frais-impayes';
  static const String memosEtudiants = '/memos-etudiants';
  static const String documentRessource = '/document-ressource';
  static const String infosCarteVirtuelle = '/infos_carte_virtuelle';
  static const String fichiersCahierTexte = '/fichiers-cahier-texte';
  static const String photoEtudiant = '/photo-etudiant';
  static const String updateProfilePicture = '/update-profile-picture'; // Mock endpoint for profile picture upload
  static String userDelete(String userId) => '/user/$userId';
}
