import 'package:equatable/equatable.dart';

/// <PERSON><PERSON><PERSON> representing a professor payment
class ProfessorPaymentEntity extends Equatable {
  final String operationNumber;
  final double netAmount;
  final String status;

  const ProfessorPaymentEntity({
    required this.operationNumber,
    required this.netAmount,
    required this.status,
  });

  @override
  List<Object?> get props => [
    operationNumber,
    netAmount,
    status,
  ];
}
