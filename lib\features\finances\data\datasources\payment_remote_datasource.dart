import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:kairos/core/api/api_exception.dart';
import 'package:kairos/core/error/exceptions.dart';

import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../models/payment_request_model.dart';
import '../models/payment_response_model.dart';
import '../models/professor_payment_model.dart';

/// Abstract interface for payment remote data source
abstract class PaymentRemoteDataSource {
  /// Process payment request via API
  Future<PaymentResponseModel> processPayment(PaymentRequestModel paymentRequest);

  /// Get list of professor payments from API
  Future<List<ProfessorPaymentModel>> getProfessorPayments({
    required String codeEtab,
    required String telephone,
    required String codeUtilisateur,
  });
}

/// Implementation of PaymentRemoteDataSource
class PaymentRemoteDataSourceImpl implements PaymentRemoteDataSource {
  final ApiClient apiClient;

  PaymentRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<PaymentResponseModel> processPayment(PaymentRequestModel paymentRequest) async {
    try {
      final response = await apiClient.postWithToken(
        ApiEndpoints.payment,
        data: paymentRequest.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        return PaymentResponseModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw ServerException('Erreur d\'initiation du paiement: ${response.statusCode}');
      }
    } on DioException catch (e) {
      debugPrint('FinancesRemoteDataSourceImpl: DioException: $e');
      debugPrint('FinancesRemoteDataSourceImpl: DioException response status: ${e.response!.statusCode}');
      final response = e.response!.data;
      debugPrint('FinancesRemoteDataSourceImpl: Decoded response: $response');
      if (response != null) {
        final apiException = ApiException.fromJson(
          response,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        debugPrint('PaymentRemoteDataSourceImpl: NetworkException: ${e.message}');
        throw NetworkException('Erreur de connexion lors de la récupération de la carte virtuelle: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la récupération de la carte virtuelle: $e');
    }
  }

  @override
  Future<List<ProfessorPaymentModel>> getProfessorPayments({
    required String codeEtab,
    required String telephone,
    required String codeUtilisateur,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    // Mock payment data with different statuses
    final mockPayments = [
      ProfessorPaymentModel(
        operationNumber: 'PMT_PROF-1-2025-7-769',
        netAmount: 38000,
        status: 'Annulé',
      ),
      ProfessorPaymentModel(
        operationNumber: 'PMT_PROF-1-2025-7-768',
        netAmount: 30000,
        status: 'Validé',
      ),
      ProfessorPaymentModel(
        operationNumber: 'PMT_PROF-1-2025-7-767',
        netAmount: 40000,
        status: 'Validé',
      ),
      ProfessorPaymentModel(
        operationNumber: 'PMT_PROF-1-2025-7-766',
        netAmount: 35000,
        status: 'Validé',
      ),
      ProfessorPaymentModel(
        operationNumber: 'PMT_PROF-1-2025-7-765',
        netAmount: 50000,
        status: 'Validé',
      ),
      ProfessorPaymentModel(
        operationNumber: 'PMT_PROF-1-2025-7-764',
        netAmount: 42000,
        status: 'En attente',
      ),
      ProfessorPaymentModel(
        operationNumber: 'PMT_PROF-1-2025-7-763',
        netAmount: 28000,
        status: 'Rejeté',
      ),
      ProfessorPaymentModel(
        operationNumber: 'PMT_PROF-1-2025-7-762',
        netAmount: 45000,
        status: 'Validé',
      ),
    ];

    return mockPayments;
  }
}
