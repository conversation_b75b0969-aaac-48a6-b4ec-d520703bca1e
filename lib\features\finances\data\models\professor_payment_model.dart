import '../../domain/entities/professor_payment_entity.dart';

/// Model for professor payment data
class ProfessorPaymentModel {
  final String operationNumber;
  final double netAmount;
  final String status;

  ProfessorPaymentModel({
    required this.operationNumber,
    required this.netAmount,
    required this.status,
  });

  /// Create model from JSON response
  factory ProfessorPaymentModel.fromJson(Map<String, dynamic> json) {
    return ProfessorPaymentModel(
      operationNumber: json['operationNumber'] as String,
      netAmount: (json['netAmount'] as num).toDouble(),
      status: json['status'] as String,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'operationNumber': operationNumber,
      'netAmount': netAmount,
      'status': status,
    };
  }

  /// Convert model to entity
  ProfessorPaymentEntity toEntity() {
    return ProfessorPaymentEntity(
      operationNumber: operationNumber,
      netAmount: netAmount,
      status: status,
    );
  }

  /// Create model from entity
  factory ProfessorPay<PERSON>Model.fromEntity(ProfessorPaymentEntity entity) {
    return ProfessorPaymentModel(
      operationNumber: entity.operationNumber,
      netAmount: entity.netAmount,
      status: entity.status,
    );
  }
}
