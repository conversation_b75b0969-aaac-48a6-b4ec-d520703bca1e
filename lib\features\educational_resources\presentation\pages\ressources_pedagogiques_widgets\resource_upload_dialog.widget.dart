import 'package:flutter/material.dart';
import 'package:kairos/core/theme/color_schemes.dart';

/// ResourceUploadDialog
/// A modern, minimalist dialog for uploading a pedagogical resource.
/// - Presents title with a close button
/// - Contains three text fields: Classe, Objet, Description (multiline)
/// - Primary action area:
///   • Initially shows a centered "Importer un fichier" ElevatedButton
///   • After mock selecting a file (toggled via setState), shows filename
///     and the primary button visually changes label to "Publier"
/// - Styling uses rounded corners, subtle borders, comfortable spacing,
///   and uses AppColorSchemes.ressourcesPedagogiquesColor for primary accents.
/// NOTE:
/// - This widget is UI-only. No real upload or publish actions are performed.
/// - Keep this dialog self-contained and reusable.
class ResourceUploadDialog extends StatefulWidget {
  const ResourceUploadDialog({super.key});

  @override
  State<ResourceUploadDialog> createState() => _ResourceUploadDialogState();
}

class _ResourceUploadDialogState extends State<ResourceUploadDialog> {
  // Controllers for form inputs - kept simple per scope.
  final TextEditingController _classeController = TextEditingController();
  final TextEditingController _objetController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  // Mock state to simulate a selected file.
  String? _selectedFileName;

  // Local UI-only uploading flag.
  // When true, the upload icon is replaced with a small white CircularProgressIndicator.
  bool _isUploading = false;

  @override
  void dispose() {
    _classeController.dispose();
    _objetController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  // Mock file select / publish action with minimal changes.
  // Wrap with async + try/finally to ensure the spinner resets even on errors.
  Future<void> _onPrimaryAction() async {
    // Start "upload/attach" visual state
    if (mounted) setState(() => _isUploading = true);
    try {
      // If no file yet, simulate "importer" by attaching a mock file.
      // If already selected, treat as "publier" (visual only) and close the dialog.
      if (_selectedFileName == null) {
        // Simulate a small async delay to better visualize the progress indicator.
        await Future<void>.delayed(const Duration(milliseconds: 350));
        if (mounted) {
          setState(() {
            _selectedFileName = 'document_exemple.pdf';
          });
        }
      } else {
        // Visual-only "Publier" action. Close dialog for feedback.
        // Keep existing behavior intact.
        Navigator.of(context).maybePop();
      }
    } finally {
      // Always reset uploading flag; guard with mounted in case dialog closed.
      if (mounted) setState(() => _isUploading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final surface = theme.colorScheme.surface;
    final outline = theme.colorScheme.outline;

    return Dialog(
      insetPadding: const EdgeInsets.all(10),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.0)),
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          minWidth: 320,
          maxWidth: 520,
        ),
        child: Material(
          color: surface,
          borderRadius: BorderRadius.circular(16.0),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                 // Accessible, responsive dialog header
                 // - Background: ressourcesPedagogiquesColor
                 // - Title: centered, white, bold
                 // - Close button: top-left, aligned, min 40x40 touch target
                 Container(
                   width: double.infinity,
                   decoration: const BoxDecoration(
                     color: AppColorSchemes.ressourcesPedagogiquesColor,
                     borderRadius: BorderRadius.only(
                       topLeft: Radius.circular(16),
                       topRight: Radius.circular(16),
                     ),
                   ),
                   // Padding ensures comfortable touch and visual spacing
                   padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                   child: Stack(
                     alignment: Alignment.center,
                     children: [
                       // Close button at top-left with 40x40 min size and sufficient contrast
                       Align(
                         alignment: Alignment.topRight,
                         child: SizedBox(
                           width: 40,
                           height: 40,
                           // IconButton already has internal padding; SizedBox enforces hit box
                           child: IconButton(
                             tooltip: 'Fermer',
                             onPressed: () => Navigator.of(context).pop(),
                             style: ButtonStyle(
                               shape: WidgetStateProperty.all(CircleBorder(side:  BorderSide(color: Colors.white, width: .1))),
                             ),
                             icon: const Icon(Icons.close),
                             color: Colors.white, // #FFFFFF for contrast on colored bg
                           ),
                         ),
                       ),
                       // Centered title, bold, white text, accessible size
                       Padding(
                         // Slight left padding to visually align baseline considering icon width
                         padding: const EdgeInsets.symmetric(horizontal: 48.0),
                         child: Text(
                           'Nouvelle ressource pédagogique',
                           textAlign: TextAlign.center,
                           style: theme.textTheme.titleMedium?.copyWith(
                                 // Use a clear, bold, accessible font size and weight
                                 // titleMedium usually ~16-18sp; enforce at least 18 for readability
                                 fontSize: (theme.textTheme.titleMedium?.fontSize ?? 18).clamp(18.0, 22.0),
                                 fontWeight: FontWeight.w800,
                                 color: Colors.white, // #FFFFFF
                               ) ??
                               const TextStyle(
                                 fontSize: 18,
                                 fontWeight: FontWeight.w800,
                                 color: Colors.white,
                               ),
                         ),
                       ),
                     ],
                   ),
                 ),
                  Padding(
                    padding: const EdgeInsets.only(left: 24, right: 24, bottom: 12, top: 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(height: 12),
                    // Classe
                    _LabeledField(
                      label: 'Classe',
                      child: TextFormField(
                        controller: _classeController,
                        decoration: _inputDecoration(outline),
                        textInputAction: TextInputAction.next,
                      ),
                    ),
                    const SizedBox(height: 12),
                    // Objet
                    _LabeledField(
                      label: 'Objet',
                      child: TextFormField(
                        controller: _objetController,
                        decoration: _inputDecoration(outline),
                        textInputAction: TextInputAction.next,
                      ),
                    ),
                    const SizedBox(height: 12),
                    // Description
                    _LabeledField(
                      label: 'Description',
                      child: TextFormField(
                        controller: _descriptionController,
                        decoration: _inputDecoration(outline),
                        maxLines: 4,
                        minLines: 3,
                        textInputAction: TextInputAction.newline,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // File name + clear button (conditionally rendered)
                    // Shown only when a file is selected. Allows clearing selection.
                    if (_selectedFileName != null) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              _selectedFileName!,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(fontSize: 13),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.redAccent),
                            tooltip: 'Supprimer le fichier',
                            onPressed: () => setState(() => _selectedFileName = null),
                          ),
                        ],
                      ),
                    ],
                    const SizedBox(height: 8),
                    // Primary action area (unchanged behavior, preserves theming)
                    Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColorSchemes.ressourcesPedagogiquesColor,
                              foregroundColor: Colors.white,
                              // Keep original button paddings; fixed syntax.
                              padding: const EdgeInsets.symmetric(horizontal: 18.0, vertical: 12.0),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              textStyle: theme.textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w600),
                              elevation: 0,
                            ),
                            // Ensure we invoke the async handler that toggles _isUploading with try/finally.
                            onPressed: _onPrimaryAction,
                            // Child updated: leading area shows either an upload icon or a small white progress indicator.
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Leading icon area - stays same width to keep layout stable.
                                // When not uploading: show a white upload icon.
                                // When uploading: show a small circular progress indicator.
                                if (!_isUploading) ...[
                                  const Icon(Icons.upload, color: Colors.white, size: 18),
                                ] else ...[
                                  const SizedBox(
                                    width: 18,
                                    height: 18,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  ),
                                ],
                                const SizedBox(width: 8),
                                Text(_selectedFileName == null ? 'Importer un fichier' : 'Publier'),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                      ]
                    ),
                  )
                ],
              ),
            ),
         
        ),
      ),
    );
  }

  // Reusable minimalist input decoration
  InputDecoration _inputDecoration(Color outline) {
    return InputDecoration(
      isDense: true,
      contentPadding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0),
        borderSide: BorderSide(color: outline.withOpacity(0.4)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0),
        borderSide: const BorderSide(color: AppColorSchemes.ressourcesPedagogiquesColor, width: 1.4),
      ),
      hintStyle: const TextStyle(color: Colors.black54),
    );
  }
}

/// Small helper to keep label and field tidy and readable
class _LabeledField extends StatelessWidget {
  final String label;
  final Widget child;

  const _LabeledField({required this.label, required this.child});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: onSurface.withOpacity(0.8),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 6),
        child,
      ],
    );
  }
}