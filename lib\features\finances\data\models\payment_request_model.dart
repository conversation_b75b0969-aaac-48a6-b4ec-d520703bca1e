import 'package:kairos/core/device_info.dart';

import '../../domain/entities/payment_request_entity.dart';

/// Model for payment request data
class PaymentRequestModel extends DeviceInfo {
  final double montantOperation;
  final String codeEtab;
  final String codeEtudiant;
  final String moyenPaiement;

  const PaymentRequestModel({
    required this.montantOperation,
    required this.codeEtab,
    required this.codeEtudiant,
    required this.moyenPaiement,
    required super.numeroTelephone,
    required super.marqueTelephone,
    required super.modelTelephone,
    required super.imeiTelephone,
    required super.numeroSerie,
  });

  /// Convert entity to model
  factory PaymentRequestModel.fromEntity(PaymentRequestEntity entity) {
    return PaymentRequestModel(
      montantOperation: entity.montantOperation,
      codeEtab: entity.codeEtab,
      numeroTelephone: entity.numeroTelephone,
      marqueTelephone: entity.marqueTelephone,
      modelTelephone: entity.modelTelephone,
      imeiTelephone: entity.imeiTelephone,
      numeroSerie: entity.numeroSerie,
      codeEtudiant: entity.codeEtudiant,
      moyenPaiement: entity.moyenPaiement,
    );
  }

  /// Convert model to JSON for API request
  @override
  Map<String, dynamic> toJson() {
    return {
      'montantOperation': montantOperation,
      'codeEtab': codeEtab,
      'codeEtudiant': codeEtudiant,
      'moyenPaiement': moyenPaiement,
      'numeroTelephone': numeroTelephone,
      'marqueTelephone': marqueTelephone,
      'modelTelephone': modelTelephone,
      'imeiTelephone': imeiTelephone,
      'numeroSerie': numeroSerie,
    };
  }
}
