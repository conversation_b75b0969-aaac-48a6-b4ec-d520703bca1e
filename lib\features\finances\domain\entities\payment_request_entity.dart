import 'package:kairos/core/device_info.dart';

/// Entity representing a payment request
class PaymentRequestEntity extends DeviceInfo {
  final double montantOperation;
  final String codeEtab;
  final String codeEtudiant;
  final String moyenPaiement;

  const PaymentRequestEntity({
    required this.montantOperation,
    required this.codeEtab,
    required this.codeEtudiant,
    required this.moyenPaiement,
    required super.numeroTelephone,
    required super.marqueTelephone,
    required super.modelTelephone,
    required super.imeiTelephone,
    required super.numeroSerie,
  });

  @override
  List<Object?> get props => [
    montantOperation,
    codeEtab,
    codeEtudiant,
    moyenPaiement,
    numeroTelephone,
    marqueTelephone,
    modelTelephone,
    imeiTelephone,
    numeroSerie,
  ];
}
