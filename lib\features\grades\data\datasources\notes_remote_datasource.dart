import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart'; // Import material for debugPrint

import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../models/notes_evaluation_model.dart';
import '../../../../core/api/api_exception.dart';
import '../../../../core/error/exceptions.dart';

/// Abstract interface for notes remote data source
abstract class NotesRemoteDataSource {
  /// Get notes evaluations from API
  Future<List<NotesEvaluationModel>> getNotesEvaluations({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get filtered notes evaluations from API by date range
  Future<List<NotesEvaluationModel>> getFilteredNotesEvaluations({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    required String dateDebut,
    required String dateFin,
  });
}

/// Implementation of NotesRemoteDataSource
class NotesRemoteDataSourceImpl implements NotesRemoteDataSource {
  final ApiClient apiClient;

  NotesRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<NotesEvaluationModel>> getNotesEvaluations({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur for PAR profile if provided
      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      // Make HTTP GET request to the notesEvaluations endpoint
      final response = await apiClient.getWithToken(
        ApiEndpoints.notesEvaluations,
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes)
      );


       final decodedResponse = latin1.decode(response.data);
       final jsonResponse = jsonDecode(decodedResponse);

      // Debug print the raw response data
      debugPrint('NotesRemoteDataSourceImpl: getNotesEvaluations raw response data: $jsonResponse');

      // Parse response data
      if (jsonResponse is List) {
        return jsonResponse
            .map((json) => NotesEvaluationModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Invalid response format: expected List but got ${response.data.runtimeType}');
      }
    } on DioException catch (e) {
      debugPrint('NotesRemoteDataSourceImpl: DioException: $e');
      debugPrint('NotesRemoteDataSourceImpl: DioException response: ${e.response}');
      debugPrint('NotesRemoteDataSourceImpl: DioException DAT -->: ${e.response?.data}');
      if (e.response != null && e.response!.data is Map<String, dynamic>) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode
        );
        throw ServerException(apiException.getUserMessage());
      } else if (e.response != null && e.response!.data is List) {
        throw ServerException('Erreur de format de réponse inattendue lors de la récupération du cahier de texte. Veuillez réessayer plus tard.');
      }
      else {
        // Handle network error
        debugPrint('ActivateSchoolRemoteDatasourceImpl: NetworkException: ${e.message}');
        throw ServerException('Erreur de connexion lors de l\'activation de l\'école: ${e.message}');
      }
    } catch (e) {
      debugPrint('NotesRemoteDataSourceImpl - Error in getNotesEvaluations: $e');
      throw ServerException('Erreur inattendue lors de la récupération des notes & évaluations. Veuillez réessayer plus tard.');
    }
  }

  @override
  Future<List<NotesEvaluationModel>> getFilteredNotesEvaluations({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    required String dateDebut,
    required String dateFin,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
        'dateDebut': dateDebut,
        'dateFin': dateFin,
      };

      // Add codeUtilisateur for PAR profile if provided
      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      debugPrint('NotesRemoteDataSourceImpl: Calling getFilteredNotesEvaluations with params: $queryParameters');

      // Make HTTP GET request to the notesEvaluationsFiltres endpoint
      final response = await apiClient.getWithToken(
        ApiEndpoints.notesEvaluationsFiltres,
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes)
      );

      // Decode response data
      final String responseString = latin1.decode(response.data);
      final jsonResponse = json.decode(responseString);

      // Debug print the raw response data
      debugPrint('NotesRemoteDataSourceImpl: getFilteredNotesEvaluations raw response data: $jsonResponse');

      // Parse response data
      if (jsonResponse is List) {
        return jsonResponse
            .map((json) => NotesEvaluationModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Invalid response format: expected List but got ${response.data.runtimeType}');
      }
    } on DioException catch (e) {
      debugPrint('NotesRemoteDataSourceImpl: DioException: $e');
      debugPrint('NotesRemoteDataSourceImpl: DioException response: ${e.response}');
      debugPrint('NotesRemoteDataSourceImpl: DioException DAT -->: ${e.response?.data}');
      final jsonResponse = jsonDecode(latin1.decode(e.response!.data));

      if (e.response != null && jsonResponse is Map<String, dynamic>) {
        // Handle API error response
        final apiException = ApiException.fromJson(
         jsonResponse,
          e.response!.statusCode
        );
        throw ServerException(apiException.getUserMessage());
      } else if (e.response != null && e.response!.data is List) {
        throw ServerException('Erreur de format de réponse inattendue lors de la récupération des notes. Veuillez réessayer plus tard.');
      }
      else {
        // Handle network error
        debugPrint('NotesRemoteDataSourceImpl: NetworkException: ${e.message}');
        throw ServerException('Erreur de connexion lors de la récupération des notes: ${e.message}');
      }
    } catch (e) {
      debugPrint('NotesRemoteDataSourceImpl - Error in getFilteredNotesEvaluations: $e');
      throw ServerException('Erreur inattendue lors de la récupération des notes & évaluations. Veuillez réessayer plus tard.');
    }
  }
}
