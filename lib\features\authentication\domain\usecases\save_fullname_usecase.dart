import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/auth_repository.dart';

/// Use case for saving user's full name
class SaveFullNameUseCase {
  final AuthRepository repository;

  SaveFullNameUseCase(this.repository);

  /// Execute the save full name use case
  ///
  /// [fullName] - The user's full name to save
  /// Returns [Either<Failure, dynamic>] - Success or failure result
  Future<Either<Failure, dynamic>> call(String fullName) async {
    return await repository.saveFullName(fullName);
  }
}
