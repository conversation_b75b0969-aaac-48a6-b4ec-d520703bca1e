import 'package:kairos/features/schools/data/models/user_profile_model.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../../../../core/api/api_exception.dart';
import '../../../../core/error/exceptions.dart';
import '../models/etablissement.dart';
import '../models/financial_status_model.dart';

/// Abstract interface for schools remote data source
abstract class SchoolsRemoteDataSource {
  /// Get list of schools
  Future<List<Etablissement>> getAllAvailableSchools();
  Future<List<UserProfileModel>> getUserSchools(String phoneNumber);

  /// Check financial status for a student
  Future<FinancialStatusModel> checkFinancialStatus({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}

/// Implementation of SchoolsRemoteDataSource
class SchoolsRemoteDataSourceImpl implements SchoolsRemoteDataSource {
  final ApiClient apiClient;

  SchoolsRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<Etablissement>> getAllAvailableSchools() async {
    try {
      // Make HTTP POST request to the allAvailableSchools endpoint
      final response = await apiClient.get(ApiEndpoints.allAvailableSchools);

      // Assuming the response data is a list of school objects
      if (response.data is List) {
        final List<dynamic> schoolJsonList = response.data;

        // Parse the list of school objects
        final List<Etablissement> schools = schoolJsonList
            .map((json) => Etablissement.fromJson(json))
            .toList();

        return schools;
      } else {
        // Handle unexpected response format
        throw ServerException('Unexpected response format');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion! Veuillez réessayer plus tard.');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors du chargement des écoles: $e');
    }
  }

  @override
  Future<List<UserProfileModel>> getUserSchools(String phoneNumber) async {
    try {
      // Make HTTP POST request to the studentSchoolList endpoint with phone number in the body and token in headers
      final response = await apiClient.postWithToken(
        ApiEndpoints.studentSchoolList,
        data: {'numeroTelephone': phoneNumber},
      );

      // Assuming the response data is a list of user profile objects
      if (response.data is List) {
        final List<dynamic> userProfileJsonList = response.data;

        // Parse the list of user profile objects
        final List<UserProfileModel> userProfiles = userProfileJsonList
            .map((json) => UserProfileModel.fromJson(json))
            .toList();

        return userProfiles;
      } else {
        // Handle unexpected response format
        debugPrint('SchoolsRemoteDataSourceImpl: getUserSchools Unexpected response format: ${response.data}');
        throw ServerException('Format de réponse inattendu');
      }
    } 
     on DioException catch (e) {
      debugPrint('SchoolsRemoteDataSourceImpl: getUserSchools DioException--->: ${e}');
      debugPrint('SchoolsRemoteDataSourceImpl: getUserSchools DioException response: ${e.response}');
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        debugPrint('SchoolsRemoteDataSourceImpl: getUserSchools NetworkException: ${e.message}');
        throw NetworkException('Erreur de connexion! Veuillez réessayer plus tard.');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors du chargement des écoles: $e');
    }
  }

  @override
  Future<FinancialStatusModel> checkFinancialStatus({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Make HTTP GET request to the financial status endpoint with query parameters
      final queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      if (codeUtilisateur != null) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      final response = await apiClient.getWithToken(
        ApiEndpoints.statutFinancierEtudiantTuteur,
        queryParameters: queryParameters,
      );

      // Parse the response data
      if (response.data is Map<String, dynamic>) {
        final Map<String, dynamic> financialStatusJson = response.data;
        debugPrint('REMOTE DATASOURCE --> FinancialStatusModel: $financialStatusJson');
        // Parse the financial status object
        final FinancialStatusModel financialStatus = FinancialStatusModel.fromJson(financialStatusJson);

        return financialStatus;
      } else {
        // Handle unexpected response format
        throw ServerException('Unexpected response format for financial status');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        debugPrint('SchoolsRemoteDataSourceImpl: checkFinancialStatus NetworkException: ${e.message}');
        throw NetworkException('Erreur de connexion! Veuillez réessayer plus tard.');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la vérification du statut financier: $e');
    }
  }
}