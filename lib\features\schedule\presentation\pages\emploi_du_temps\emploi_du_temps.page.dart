import 'package:kairos/features/schedule/domain/entities/emploi_temps_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/core/widgets/common/empty_message.widget.dart';
import 'package:kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/core/di/injection_container.dart';
import '../../bloc/schedule_cubit.dart';
import '../../bloc/schedule_state.dart';
import '../../bloc/course_content_entry_cubit.dart';
import 'emploi_temps_item.widget.dart';
import 'package:kairos/core/utils/date_utils.dart' as date_utils;
import 'course_content_entry_form.widget.dart';

class EmploiDuTempsPage extends StatefulWidget {
  final EtablissementUtilisateur? school;
  final EnfantTuteurEntity? etudiant;

  const EmploiDuTempsPage({
    super.key,
    this.school,
    this.etudiant,
  });

  @override
  State<EmploiDuTempsPage> createState() => _EmploiDuTempsPageState();
}

class _EmploiDuTempsPageState extends State<EmploiDuTempsPage> with TickerProviderStateMixin {
  // Helper method to get weekday abbreviation in French
  String _getWeekdayAbbr(DateTime date) {
    const weekdays = {
      1: 'LUN',
      2: 'MAR',
      3: 'MER',
      4: 'JEU',
      5: 'VEN',
      6: 'SAM',
      7: 'DIM',
    };
    return weekdays[date.weekday] ?? '';
  }

  bool _isSearchBarVisible = false;
  late TextEditingController _searchController;
  late AnimationController _searchAnimationController;
  late AuthLocalDataSource _authLocalDataSource;

  // Date filter state
  String? _startDateFilter;
  String? _endDateFilter;

  // Weekly navigation state
  DateTime _currentWeekStart = DateTime.now();
  bool _isFromCahierTexte = false;

  // PageView controller for navigation between slides
  late PageController _pageController;

  // Selected schedule item for course content entry
  EmploiTempsEntity? _selectedScheduleItem;

  // Filtered data for client-side text search
  List<List<EmploiTempsEntity>> _filteredScheduleData = [];
  final List<List<EmploiTempsEntity>> _allScheduleData = [];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _authLocalDataSource = sl<AuthLocalDataSource>();
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _searchController.addListener(_filterScheduleEntries);

    // Initialize PageController
    _pageController = PageController();

    // Check if navigated from cahier_texte
    WidgetsBinding.instance.addPostFrameCallback((_) {
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    debugPrint("Arguments: $args");
    setState(() {
    _isFromCahierTexte = args?['isAddingFromCahierTexte'] ?? false;
    _currentWeekStart = _getWeekStart(DateTime.now());
    });
    debugPrint("isFromCahierTexte: $_isFromCahierTexte");
    // Initialize current week to today's week
    // Load schedule data
    _loadScheduleData();
    });
  }

  // Helper method to get the start of the week (Monday)
  DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  // Helper method to get the end of the week (Sunday)
  DateTime _getWeekEnd(DateTime weekStart) {
    return weekStart.add(const Duration(days: 6));
  }

  // Helper method to filter schedule data by week
  List<List<EmploiTempsEntity>> _filterScheduleByWeek(List<List<EmploiTempsEntity>> scheduleData, DateTime weekStart) {
    final weekEnd = _getWeekEnd(weekStart);

    return scheduleData.where((daySchedule) {
      if (daySchedule.isEmpty) return false;

      // Parse the date from the first item in the day
      try {
        final dateStr = daySchedule.first.date;
        final dateParts = dateStr.split('/');
        if (dateParts.length >= 3) {
          final day = int.parse(dateParts[0]);
          final month = int.parse(dateParts[1]);
          final year = dateParts.length > 2 ? int.parse(dateParts[2]) : DateTime.now().year;

          final itemDate = DateTime(year, month, day);
          return itemDate.isAfter(weekStart.subtract(const Duration(days: 1))) &&
                 itemDate.isBefore(weekEnd.add(const Duration(days: 1)));
        }
      } catch (e) {
        debugPrint('Error parsing date: $e');
      }
      return false;
    }).toList();
  }

  // Navigate to previous week
  void _navigateToPreviousWeek() {
    setState(() {
      _currentWeekStart = _currentWeekStart.subtract(const Duration(days: 7));
      _filteredScheduleData = _filterScheduleByWeek(_allScheduleData, _currentWeekStart);
    });
  }

  // Navigate to next week
  void _navigateToNextWeek() {
    setState(() {
      _currentWeekStart = _currentWeekStart.add(const Duration(days: 7));
      _filteredScheduleData = _filterScheduleByWeek(_allScheduleData, _currentWeekStart);
    });
  }

  // Navigate to course content entry page
  void _navigateToCourseContentEntry(EmploiTempsEntity scheduleItem) {
    setState(() {
      _selectedScheduleItem = scheduleItem;
    });
    _pageController.animateToPage(
      1,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  // Navigate back to schedule list
  void _navigateBackToScheduleList() {
    setState(() {
      _selectedScheduleItem = null;
    });
    _pageController.animateToPage(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _loadScheduleData() async {
    try {
      // Get the school code from widget or fallback to a default
      final codeEtab = widget.school?.codeEtab ?? '';

      // Get phone number from AuthLocalDataSource
      final telephone = await _authLocalDataSource.getPhoneNumber() ?? '';

      // For ETU users, codeEtudiant is the same as codeUtilisateur
      // For PAR users, codeEtudiant comes from the selected child
      final codeEtudiant = widget.etudiant?.codeEtudiant ?? widget.school?.codeUtilisateur ?? '';
      final codeUtilisateur = widget.etudiant != null ? widget.school?.codeUtilisateur : null;

      if (mounted && codeEtab.isNotEmpty && telephone.isNotEmpty && codeEtudiant.isNotEmpty) {
        context.read<EmploiDuTempsCubit>().loadScheduleData(
          codeEtab: codeEtab,
          telephone: telephone,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        );
      } else {
        debugPrint('Missing required parameters for schedule data loading: codeEtab=$codeEtab, telephone=$telephone, codeEtudiant=$codeEtudiant');
      }
    } catch (e) {
      debugPrint('Error loading schedule data: $e');
    }
  }

  void _filterScheduleEntries() async { // Made async to await getPhoneNumber
    final query = _searchController.text.toLowerCase();
    debugPrint("Filtering schedule entries: query=$query, startDateFilter=$_startDateFilter, endDateFilter=$_endDateFilter");
    // Check if both date filters are set
    if (_startDateFilter != null && _endDateFilter != null) {
      try {
        // Get the school code from widget or fallback to a default
        final codeEtab = widget.school?.codeEtab ?? '';

        // Get phone number from AuthLocalDataSource
        final telephone = await _authLocalDataSource.getPhoneNumber() ?? '';

        // For ETU users, codeEtudiant is the same as codeUtilisateur
        // For PAR users, codeEtudiant comes from the selected child
        final codeEtudiant = widget.etudiant?.codeEtudiant ?? widget.school?.codeUtilisateur ?? '';
        final codeUtilisateur = widget.etudiant != null ? widget.school?.codeUtilisateur : null;

        if (mounted && codeEtab.isNotEmpty && telephone.isNotEmpty && codeEtudiant.isNotEmpty) {
          // Call the cubit method to load filtered data
          context.read<EmploiDuTempsCubit>().loadFilteredScheduleData(
            codeEtab: codeEtab,
            telephone: telephone,
            codeEtudiant: codeEtudiant,
            codeUtilisateur: codeUtilisateur,
            startDate: _startDateFilter!,
            endDate: _endDateFilter!,
          );
        } else {
          debugPrint('Missing required parameters for filtered schedule data loading: codeEtab=$codeEtab, telephone=$telephone, codeEtudiant=$codeEtudiant');
        }
      } catch (e) {
        debugPrint('Error loading filtered schedule data: $e');
      }
    } else {
      // Existing logic for text search or no filters
      setState(() {
        if (query.isEmpty) {
          // Reset to original data - will be handled by BLoC state
          _filteredScheduleData = _allScheduleData;
        } else {
          // Apply client-side filtering to current schedule data based on query
          // This will be implemented after BoC integration
          debugPrint("Applying text filter: query=$query");
          _filteredScheduleData = _allScheduleData.map((daySchedule) {
            return daySchedule.where((entry) {
              return entry.cours.toLowerCase().contains(query) ||
                     entry.professeur.toLowerCase().contains(query) ||
                     entry.heure.toLowerCase().contains(query) ||
                     entry.salle.toLowerCase().contains(query) ||
                     entry.classe.toLowerCase().contains(query) ||
                     entry.semestre.toLowerCase().contains(query) ||
                     entry.type.toLowerCase().contains(query) ||
                     entry.date.toLowerCase().contains(query);
            }).toList();
          }).toList();
          // Note: Client-side text filtering logic needs to be added here
          // based on the current state's scheduleData.
        }
      });
    }
  }

  // Method to handle date filter changes
  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDateFilter = dateRange['startDate'];
      _endDateFilter = dateRange['endDate'];
    });
    debugPrint("startDateFilter: $_startDateFilter, endDateFilter: $_endDateFilter");
    _filterScheduleEntries();
  }

  // Method to clear date filters
  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    // Reapply filters without date constraint
    _filterScheduleEntries();
  }

  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (_isSearchBarVisible) {
        _searchAnimationController.forward();
      } else {
        _searchAnimationController.reverse();
        _searchController.clear();
        _startDateFilter = null; // Clear date filters when hidden
        _endDateFilter = null;
        _filteredScheduleData = []; // Reset filtered data
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: false,
      body: BlocListener<EmploiDuTempsCubit, EmploiDuTempsState>(
        listener: (context, state) {
          if (state is EmploiDuTempsError) {
            debugPrint("Error loading schedule data: ${state.message}");
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text("Erreur de chargement des données: Veuillez réessayer plus tard")),
            );
          }
        },
        child: PageView(
          controller: _pageController,
          physics: const NeverScrollableScrollPhysics(), // Disable swipe navigation
          children: [
            _buildScheduleListPage(),
            _buildCourseContentEntryPage(),
          ],
        ),
      ),
    );
  }

  // Build the schedule list page (slide 1)
  Widget _buildScheduleListPage() {
    return CustomScrollView(
      slivers: [
        // Add navigation buttons for weekly navigation when from cahier_texte
        CustomAppBar(
          pageSection: HeaderEnum.planning,
          isSearchBarVisible: _isSearchBarVisible,
          title: "EMPLOI DU TEMPS",
          etablissementUtilisateur: widget.school,
          enfantDuTuteur: widget.etudiant,
          onSearchTap: _toggleSearchBarVisibility,
        ),
        AnimatedBuilder(
          animation: _searchAnimationController,
          builder: (context, child) {
            return SliverPersistentHeader(
              pinned: true,
              delegate: SearchBarSliver(
                extentHeight:  _searchAnimationController.value * (_startDateFilter != null && _endDateFilter != null ? 100.0 : 60.0),
                searchController: _searchController,
                onSearchChanged: (query) => _filterScheduleEntries(),
                onDateFilterChanged: _onDateFilterChanged,
                onClearDateFilter: _clearDateFilter,
                hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
                hintText: "Rechercher un cours...",
                startDate: _startDateFilter,
                endDate: _endDateFilter,
              ),
            );
          },
        ),
        if (_isFromCahierTexte) 
          SliverToBoxAdapter(
            child: SizedBox(
              height: 60.0, // Explicitly constrain the height
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    FilledButton(
                      onPressed: _navigateToPreviousWeek,
                      style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.all(Colors.transparent),
                        minimumSize: WidgetStateProperty.all(const Size(150, 50)),
                        shape: WidgetStateProperty.all(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                            side: BorderSide(color: Colors.black),
                          ),
                        ),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.arrow_circle_left, size: 20, color: Colors.black), // Left arrow icon
                          SizedBox(width: 4), // Space between icon and text
                          Text("Précédent", style: TextStyle(color: Colors.black)),
                        ],
                      ),
                    ),
                    FilledButton(
                      onPressed: _navigateToNextWeek,
                      style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.all(Colors.transparent),
                        minimumSize: WidgetStateProperty.all(const Size(150, 50)),
                        shape: WidgetStateProperty.all(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                            side: BorderSide(color: Colors.black),
                          ),
                        ),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text("Suivant", style: TextStyle(color: Colors.black)),
                          SizedBox(width: 4), // Space between icon and text
                          Icon(Icons.arrow_circle_right, size: 20, color: Colors.black), // Right arrow icon
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        BlocBuilder<EmploiDuTempsCubit, EmploiDuTempsState>(
          builder: (context, state) {
            if (state is EmploiDuTempsLoading) {
              debugPrint("Loading schedule data...");
              return SliverFillRemaining(
                child: Center(
                  child: CustomSpinner(
                    size: 60.0,
                    strokeWidth: 5.0,
                  ),
                ),
              );
            } else if (state is EmploiDuTempsLoaded) {
              debugPrint("Schedule data loaded: ${state.scheduleData.length} days");
              _allScheduleData.clear();
              _allScheduleData.addAll(state.scheduleData);

              debugPrint("Schedule data loaded: ${state.scheduleData}");
              // Filter by current week if from cahier_texte
              if (_isFromCahierTexte ) {
                _filteredScheduleData = _filterScheduleByWeek(_allScheduleData, _currentWeekStart);
                // _filteredScheduleData = filteredData.isNotEmpty ? filteredData : _allScheduleData;
              } else {
                _filteredScheduleData = state.scheduleData;
              }

              if (_filteredScheduleData.isEmpty) {
                return SliverFillRemaining(
                  child: Center(
                    child: EmptyMessage(message: "Aucun cours trouvé"),
                  ),
                );
              }

              return SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, dayIndex) {
                    final daySchedule = _filteredScheduleData[dayIndex];
                    if (daySchedule.isEmpty) return const SizedBox.shrink();

                    // Get the date from the first schedule item of the day
                    final date = date_utils.parseDate(daySchedule.first.date);
                    final weekday = _getWeekdayAbbr(date);
                    final dateStr = "${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}";

                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 7.0, horizontal: 1.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Weekday and date column (left)
                          Padding(
                            padding: const EdgeInsets.only(right: 0, top: 4.0, left: 7.0),
                            child: SizedBox(
                              width: 60,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    weekday,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 1.5,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    dateStr,
                                    style: const TextStyle(
                                      fontSize: 11,
                                      color: Colors.black54,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Schedule items column (right)
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: daySchedule.map((scheduleItem) =>
                                GestureDetector(
                                  onTap: _isFromCahierTexte ? () => _navigateToCourseContentEntry(scheduleItem) : null,
                                  child: EmploiTempsItem(emploiTemps: scheduleItem),
                                )
                              ).toList(),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                  childCount: _filteredScheduleData.length,
                ),
              );
            } else if (state is EmploiDuTempsError) {
              debugPrint("Error loading schedule data: ${state.message}");
              return SliverFillRemaining(
                child: Center(
                  child: EmptyMessage(message: "Erreur de chargement des données. Veuillez réessayer plus tard"),
                ),
              );
            } else {
              return SliverFillRemaining(
                child: Center(
                  child: EmptyMessage(message: "Aucune donnée disponible"),
                ),
              );
            }
          },
        ),
      ],
    );
  }

  // Build the course content entry page (slide 2)
  Widget _buildCourseContentEntryPage() {
    if (_selectedScheduleItem == null) {
      return const Scaffold(
        body: Center(
          child: Text("Aucun cours sélectionné"),
        ),
      );
    }

    return BlocProvider(
      create: (context) => sl<CourseContentEntryCubit>(),
      child: CourseContentEntryForm(
        scheduleItem: _selectedScheduleItem!,
        codeEtab: widget.school?.codeEtab ?? '',
        currentUser: widget.school!,
        codeEtudiant: widget.etudiant?.codeEtudiant ?? widget.school?.codeUtilisateur ?? '',
        onSuccess: (courseName, teacherName, timeRange) {
          // Navigate back to cahier_texte page with success information
          Navigator.of(context).pop({
            'success': true,
            'courseName': courseName,
            'teacherName': teacherName,
            'timeRange': timeRange,
          });
        },
        onCancel: _navigateBackToScheduleList,
      ),
    );
  }
}
