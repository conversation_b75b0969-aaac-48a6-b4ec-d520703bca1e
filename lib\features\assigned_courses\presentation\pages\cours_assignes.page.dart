import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/core/theme/color_schemes.dart';
import 'package:kairos/core/widgets/inputs/search_bar_sliver.widget.dart'; // Import SearchBarSliver widget
import 'package:kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import '../../../../features/schools/domain/entities/etablissement_utilisateur.dart';
import '../../../../features/student_records/domain/entities/enfant_tuteur_entity.dart';
import '../bloc/assigned_courses_cubit.dart';
import '../bloc/assigned_courses_state.dart';
import '../widgets/assigned_course_item.widget.dart';
import '../widgets/course_detail_dialog.widget.dart';
import '../../../../core/di/injection_container.dart';

/// Page for displaying assigned courses for PRF profile users
class CoursAssignesPage extends StatefulWidget {
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? etudiant;


  const CoursAssignesPage({
    super.key,
    required this.school,
    this.etudiant,
  });

  @override
  State<CoursAssignesPage> createState() => _CoursAssignesPageState();
}

class _CoursAssignesPageState extends State<CoursAssignesPage> with SingleTickerProviderStateMixin {
  // Boolean to control the visibility of the search bar.
  bool _isSearchBarVisible = false;
  // Controller for the search input field.
  late TextEditingController _searchController;
  // Animation controller for the search bar's height.
  late AnimationController _searchAnimationController;

  // Data lists for courses
  List<dynamic> _allCourses = [];
  List<dynamic> _filteredCourses = [];

  final AuthLocalDataSource authLocalDataSource = sl<AuthLocalDataSource>();

  @override
  void initState() {
    super.initState();
    // Initialize search controller and add a listener for text changes.
    _searchController = TextEditingController();
    _searchController.addListener(() => _filterCourses(_searchController.text));

    // Initialize animation controller for search bar
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300), // Animation duration
    );

    _loadAssignedCourses();
  }

  @override
  void dispose() {
    // Dispose of all controllers to prevent memory leaks
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  /// Load assigned courses data
  void _loadAssignedCourses() async {
    debugPrint("Loading assigned courses data");
    final String? telephone = await authLocalDataSource.getPhoneNumber();
    if (telephone == null) return;
    context.read<AssignedCoursesCubit>().loadAssignedCourses(
          codeEtab: widget.school.codeEtab,
          telephone: telephone,
          codeEtudiant: widget.etudiant?.codeEtudiant ?? widget.school.codeUtilisateur,
          codeUtilisateur: widget.etudiant != null ? widget.school.codeUtilisateur : null,
        );
  }

  /// Toggles the visibility of the search bar with an animation.
  /// When the search bar is hidden, it clears the search text and re-filters courses.
  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible; // Toggle visibility state
      if (!_isSearchBarVisible) {
        // If search bar is now hidden, reverse the animation and clear search
        _searchAnimationController.reverse();
        _searchController.clear();
        _filterCourses(''); // Reset filter to show all courses
      } else {
        // If search bar is now visible, play the animation forward
        _searchAnimationController.forward();
      }
    });
  }

  /// Filters the list of assigned courses based on the search input.
  /// If the search text is empty, all courses are displayed.
  /// Otherwise, courses are filtered by their name (case-insensitive).
  void _filterCourses(String searchText) {
    setState(() {
      if (searchText.isEmpty) {
        // If search text is empty, display all courses.
        _filteredCourses = List.from(_allCourses);
      } else {
        // Filter courses based on whether the course name contains the search text (case-insensitive).
        _filteredCourses = _allCourses.where((course) {
          return course.cours.toLowerCase().contains(searchText.toLowerCase());
        }).toList();
      }
    });
  }


  /// Handle refresh
  Future<void> _onRefresh() async {
    _loadAssignedCourses();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          // custom app bar
          CustomAppBar(
            pageSection: HeaderEnum.coursAssignes,
            title: "COURS DISPENSÉS",
            etablissementUtilisateur: widget.school,
            enfantDuTuteur: widget.etudiant,
            // Pass the visibility state to the CustomAppBar
            isSearchBarVisible: _isSearchBarVisible,
            // Assign the toggle function to the search icon tap
            onSearchTap: _toggleSearchBarVisibility,
          ),
          // Animated search bar using SearchBarSliver
          // This widget provides a persistent header with a search input field.
          // Its height is animated based on _searchAnimationController.
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                // The search bar should float as the user scrolls.
                floating: true,
                // The snapping behavior is typically associated with SliverAppBar.
                // SliverPersistentHeader does not have a 'snap' property directly.
                delegate: SearchBarSliver(
                  // Animate the height of the search bar. When the animation value is 0, the height is 0,
                  // effectively hiding the search bar. When it's 1, the height is 60.0.
                  // Only show the search bar if _isSearchBarVisible is true.
                  extentHeight:_searchAnimationController.value * 60.0,
                  // Pass the existing search controller to the SearchBarSliver.
                  searchController: _searchController,
                  // Assign the _filterCourses method to be called when the search text changes.
                  onSearchChanged: _filterCourses,
                  // Provide a suitable hint text for the search input field.
                  hintText: "Rechercher cours...",
                  // Since date filtering is not used, hasActiveFilter can be set to false.
                  // The search bar's appearance will primarily be controlled by its height animation.
                  hasActiveFilter: false,
                ),
              );
            }
          ),
          // Content
          BlocBuilder<AssignedCoursesCubit, AssignedCoursesState>(
            builder: (context, state) {
              if (state is AssignedCoursesLoading) {
                return const SliverFillRemaining(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              } else if (state is AssignedCoursesError) {
                return SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: AppColorSchemes.errorRed,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          state.message,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 16,
                            color: AppColorSchemes.errorRed,
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadAssignedCourses,
                          child: const Text('Réessayer'),
                        ),
                      ],
                    ),
                  ),
                );
              } else if (state is AssignedCoursesLoaded) {
                // When assigned courses are loaded, update _allCourses with the full list.
                debugPrint('all courses assigned -----------> ${state.assignedCourses}');
                _allCourses = state.assignedCourses;
                // Schedule the filtering to happen after the current build frame.
                // This prevents setState() being called during build.
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _filterCourses(_searchController.text);
                });

                if (_filteredCourses.isEmpty) {
                  return const SliverFillRemaining(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.school_outlined,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Aucun cours assigné trouvé',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return SliverPadding(
                  padding: const EdgeInsets.fromLTRB(16, 10, 16, 16),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final course = _filteredCourses[index]; // Use _filteredCourses
                        return AssignedCourseItem(
                          course: course,
                          onTap: () => _showCourseDetailDialog(course),
                        );
                      },
                      childCount: _filteredCourses.length, // Use _filteredCourses length
                    ),
                  ),
                );
              }

              return const SliverFillRemaining(
                child: Center(
                  child: Text('État inattendu'),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// Show course detail dialog
  void _showCourseDetailDialog(course) {
    showDialog(
      context: context,
      builder: (context) => CourseDetailDialog(course: course),
    );
  }
}
