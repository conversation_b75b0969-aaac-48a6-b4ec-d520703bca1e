import 'package:equatable/equatable.dart';
import '../../domain/entities/payment_response_entity.dart';
import '../../domain/entities/professor_payment_entity.dart';

/// Base payment state
abstract class PaymentState extends Equatable {
  const PaymentState();
  
  @override
  List<Object?> get props => [];
}

/// Initial payment state
class PaymentInitial extends PaymentState {
  const PaymentInitial();
}

/// Loading state during payment processing
class PaymentLoading extends PaymentState {
  const PaymentLoading();
}

/// Payment processed successfully
class PaymentInitiatedSuccess extends PaymentState {
  final PaymentResponseEntity paymentResponse;
  final double paymentAmount;

  const PaymentInitiatedSuccess({
    required this.paymentResponse,
    required this.paymentAmount,
  });

  @override
  List<Object?> get props => [paymentResponse, paymentAmount];
}

/// Payment processing failed
class PaymentInitiatedError extends PaymentState {
  final String message;

  const PaymentInitiatedError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Professor payments loaded successfully
class ProfessorPaymentsLoaded extends PaymentState {
  final List<ProfessorPaymentEntity> payments;

  const ProfessorPaymentsLoaded({
    required this.payments,
  });

  @override
  List<Object?> get props => [payments];
}
