import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/device_info.dart';
import 'package:kairos/core/error/failures.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/enums/payment_method.dart';
import '../../domain/usecases/process_payment_usecase.dart';
import '../../domain/usecases/get_professor_payments_usecase.dart';
import 'payment_state.dart';

/// Payment Cubit for managing payment state
class PaymentCubit extends Cubit<PaymentState> {
  final ProcessPaymentUseCase processPaymentUseCase;
  final GetProfessorPaymentsUseCase? getProfessorPaymentsUseCase;

  PaymentCubit({
    required this.processPaymentUseCase,
    this.getProfessorPaymentsUseCase,
  }) : super(const PaymentInitial());

  /// Process a payment request
  Future<void> processPayment({
    required double amount,
    required PaymentMethod paymentMethod,
    required String codeEtab,
    required String phoneNumber,
    required DeviceInfo deviceInfo,
    required String codeEtudiant,
  }) async {
    emit(const PaymentLoading());

    try {
      final result = await processPaymentUseCase(
        ProcessPaymentParams(
          montantOperation: amount,
          codeEtab: codeEtab,
          codeEtudiant: codeEtudiant,
          numeroTelephone: phoneNumber,
          marqueTelephone: deviceInfo.marqueTelephone,
          modelTelephone: deviceInfo.modelTelephone,
          imeiTelephone: deviceInfo.imeiTelephone,
          numeroSerie: deviceInfo.numeroSerie,
          moyenPaiement: paymentMethod.apiValue,
        ),
      );

      result.fold(
        (failure) => emit(PaymentInitiatedError(_mapFailureToMessage(failure))),
        (paymentResponse) async {
          // Launch payment URL as deeplink
          await _launchPaymentUrl(paymentResponse.paymentUrl);
          
          emit(PaymentInitiatedSuccess(
            paymentResponse: paymentResponse,
            paymentAmount: amount,
          ));
        },
      );
    } catch (e) {
      emit(PaymentInitiatedError('Une erreur inattendue s\'est produite: $e'));
    }
  }

  /// Launch payment URL using url_launcher
  Future<void> _launchPaymentUrl(String paymentUrl) async {
    try {
      final uri = Uri.parse(paymentUrl);
      debugPrint('Attempting to launch URL: $paymentUrl');
      if (await canLaunchUrl(uri)) {
        debugPrint('canLaunchUrl returned true. Launching URL...');
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication, // Launch in external app
        );
        debugPrint('URL launched successfully.');
      } else {
        debugPrint('canLaunchUrl returned false. Throwing exception.');
        throw Exception('Could not launch payment URL');
      }
    } catch (e) {
      // Log error but don't fail the payment process
      debugPrint('Failed to launch payment URL: ${e.toString()}');
      debugPrint('Error type: ${e.runtimeType}');
      debugPrint('Stack trace: ${StackTrace.current}');
    }
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return 'Erreur du serveur: ${failure.message}';
    } else if (failure is NetworkFailure) {
      return 'Erreur de connexion. Vérifiez votre connexion internet: ${failure.message}';
    } else {
      return 'Une erreur inattendue s\'est produite lors du paiement.';
    }
  }

  /// Load professor payments
  Future<void> loadProfessorPayments({
    required String codeEtab,
    required String telephone,
    required String codeUtilisateur,
  }) async {
    if (getProfessorPaymentsUseCase == null) {
      emit(const PaymentInitiatedError('Professor payments use case not available'));
      return;
    }

    emit(const PaymentLoading());

    try {
      final result = await getProfessorPaymentsUseCase!(GetProfessorPaymentsParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeUtilisateur: codeUtilisateur,
      ));

      result.fold(
        (failure) => emit(PaymentInitiatedError(_mapFailureToMessage(failure))),
        (payments) => emit(ProfessorPaymentsLoaded(payments: payments)),
      );
    } catch (e) {
      emit(PaymentInitiatedError(e.toString()));
    }
  }

  /// Reset payment state
  void resetPayment() {
    emit(const PaymentInitial());
  }
}
