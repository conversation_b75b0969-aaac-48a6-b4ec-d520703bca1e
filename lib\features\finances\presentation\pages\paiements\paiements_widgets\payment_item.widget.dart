import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:kairos/core/theme/color_schemes.dart';
import 'package:kairos/features/finances/domain/entities/professor_payment_entity.dart';
import 'payment_details_sheet_content.widget.dart';

class PaymentItem extends StatelessWidget {
  final ProfessorPaymentEntity payment;

  const PaymentItem({
    super.key,
    required this.payment,
  });

  /// Get color based on payment status
  Color _getStatusColor() {
    switch (payment.status.toLowerCase()) {
      case 'validé':
        return AppColorSchemes.successGreen;
      case 'annulé':
        return AppColorSchemes.errorRed;
      case 'rejeté':
        return AppColorSchemes.errorRed;
      case 'en attente':
        return AppColorSchemes.warningOrange;
      default:
        return AppColorSchemes.mediumGray;
    }
  }

  /// Show payment details as a draggable scrollable sheet
  void _showPaymentDetailsSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Allows the sheet to be full screen if needed
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        // Use DraggableScrollableSheet to allow content scrolling
        return DraggableScrollableSheet(
          initialChildSize: 0.6, // Initial height of the sheet
          minChildSize: 0.3,     // Minimum height
          maxChildSize: 0.9,     // Maximum height
          expand: false,         // Do not expand to fill available vertical space if content is less
          builder: (BuildContext context, ScrollController scrollController) {
            return PaymentDetailsSheetContent(
              payment: payment,
              scrollController: scrollController,
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final statusColor = _getStatusColor();
    final formatter = NumberFormat('#,###', 'fr_FR');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 7.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Theme.of(context).colorScheme.secondary, width: 1),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: InkWell(
        // Changed onTap to call the new sheet method
        onTap: () => _showPaymentDetailsSheet(context),
        borderRadius: BorderRadius.circular(8),
        child: IntrinsicHeight(
          child: Row(
            children: [
              // Left border with status color
              Container(
                width: 5,
                decoration: BoxDecoration(
                  color: statusColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    bottomLeft: Radius.circular(8),
                  ),
                ),
              ),
              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(5.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // No Operation
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'N° Opération',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            payment.operationNumber,
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      // Net à Payer
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Net à payer',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            '${formatter.format(payment.netAmount.toInt())} XOF',
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                    ],
                  ),
                ),
              ),
              // Statut
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Statut',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      payment.status,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: statusColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
