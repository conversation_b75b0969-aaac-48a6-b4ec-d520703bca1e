import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/professor_payment_entity.dart';
import '../repositories/payment_repository.dart';

/// Use case for getting professor payments
class GetProfessorPaymentsUseCase implements UseCase<List<ProfessorPaymentEntity>, GetProfessorPaymentsParams> {
  final PaymentRepository repository;

  GetProfessorPaymentsUseCase(this.repository);

  @override
  Future<Either<Failure, List<ProfessorPaymentEntity>>> call(GetProfessorPaymentsParams params) async {
    return await repository.getProfessorPayments(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}

/// Parameters for the GetProfessorPaymentsUseCase
class GetProfessorPaymentsParams extends Equatable {
  final String codeEtab;
  final String telephone;
  final String codeUtilisateur;

  const GetProfessorPaymentsParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeUtilisateur,
  });

  @override
  List<Object?> get props => [
    codeEtab,
    telephone,
    codeUtilisateur,
  ];
}
