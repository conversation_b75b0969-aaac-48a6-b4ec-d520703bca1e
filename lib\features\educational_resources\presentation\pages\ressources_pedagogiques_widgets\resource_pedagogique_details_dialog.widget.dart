import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/features/educational_resources/presentation/bloc/ressources_pedagogiques_cubit.dart';
import 'package:kairos/features/educational_resources/presentation/bloc/ressources_pedagogiques_state.dart';
import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:kairos/core/di/injection_container.dart';

class ResourcePedagogiqueDetailsDialog extends StatelessWidget {
  ResourcePedagogiqueDetailsDialog({
    super.key,
    required this.ressource,
    this.school,
    this.etudiant,
  });

  final ResourcePedagogiqueEntity ressource;
  final EtablissementUtilisateur? school;
  final EnfantTuteurEntity? etudiant;
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();

  String _getInitials(String typeRessource) {
    if (typeRessource.isEmpty) return 'RR';
    
    final words = typeRessource.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else {
      return typeRessource.length >= 2 
          ? typeRessource.substring(0, 2).toUpperCase()
          : typeRessource.toUpperCase();
    }
  }


  void _handleAttachmentTap(BuildContext context) async {
    debugPrint("ressource.documents.isEmpty: ${ressource.documents.isEmpty}");
    if (ressource.documents.isEmpty) return;

    final String? userPhoneNumber = await _authLocalDataSource.getPhoneNumber();
    if (userPhoneNumber != null && school != null) {
      context.read<RessourcesPedagogiquesCubit>().fetchRessourcePedagogiqueAttachment(
        codeEtab: school!.codeEtab,
        telephone: userPhoneNumber,
        codeEtudiant: etudiant?.codeEtudiant ?? school?.codeUtilisateur ?? '',
        idObject: ressource.idRessource,
        codeUtilisateur: etudiant != null ? school?.codeUtilisateur : null,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(horizontal: 10),
      child: SizedBox(
        width: 300,
        height: 550,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // Main dialog container
            Positioned(
              left: 0,
              top: 28,
              child: Container(
                width: 300,
                height: 540,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                    color: Colors.black.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
// Header with avatar and title
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24.0),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: 42,
                                  height: 41,
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF8E0101), // Default color for educational resources
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Text(
                                      'R', // R for Resource
                                      style: const TextStyle(

                                        fontWeight: FontWeight.w400,
                                        fontSize: 16,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 7),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        ressource.description,
                                        style: const TextStyle(

                                          fontWeight: FontWeight.w700,
                                          fontSize: 16,
                                          color: Color(0xFF8E0101),
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Soumis par ${ressource.professeur} le ${ressource.dateAjout}',
                                        style: TextStyle(

                                          fontWeight: FontWeight.w400,
                                          fontSize: 9,
                                          color: Colors.black.withValues(alpha: 0.5),
                                        ),
                                      ),
                                      const SizedBox(height: 2),
                                      Text(
                                        ressource.classe.replaceAll("[", "").replaceAll("]", ""),
                                        style: TextStyle(
                                          
                                          fontWeight: FontWeight.w400,
                                          fontSize: 9,
                                          color: Colors.black.withValues(alpha: 0.5),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 15),
                            // Object/Subject
                            Row(
                              children: [
                                RichText(
                                  text: TextSpan(
                                    style: const TextStyle(
                                      fontSize: 11,
                                      color: Colors.black,
                                      fontWeight: FontWeight.w400, // Default to normal weight for the rest of the text
                                    ),
                                    children: <TextSpan>[
                                      TextSpan(
                                        text: 'Objet : ',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w800, // Bold for "Objet :"
                                        ),
                                      ),
                                      TextSpan(
                                        text: 'Ressource pédagogique',
                                        // This will inherit the parent TextSpan's style (normal weight)
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 7),
                      Container(
                      width: 250,
                      height: 360,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                          color: Colors.black.withValues(alpha: 0.5),
                          width: 1,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(15),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [// Description/Content
                            Expanded(
                              child: SingleChildScrollView(
                                child: Text(
                                  ressource.description,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 12,
                                    color: Colors.black,
                                    height: 1.2,
                                  ),
                                ),
                              ),
                            ),
                            // Attachment section
                            
                          ],
                        ),
                      ),
                    ),
                    if (ressource.documents.isNotEmpty) ...[
                               const SizedBox(height: 20),
                               BlocBuilder<RessourcesPedagogiquesCubit, RessourcesPedagogiquesState>(
                                 builder: (context, state) {
                                   bool isLoadingAttachment = false;
                                   if (state is RessourcesPedagogiquesLoaded) {
                                     isLoadingAttachment = state.attachmentStatus == AttachmentStatus.loading;
                                   }

                                   return InkWell(
                                     onTap: () => _handleAttachmentTap(context),
                                     child: Padding(
                                       padding: const EdgeInsets.only(right: 20.0),
                                       child: Row(
                                         mainAxisAlignment: MainAxisAlignment.end,
                                         children: [
                                           if (isLoadingAttachment)
                                             const SizedBox(
                                               width: 24,
                                               height: 24,
                                               child: CircularProgressIndicator(strokeWidth: 2),
                                             )
                                           else
                                             Icon(
                                               Icons.download,
                                               size: 24,
                                               color: Colors.grey[600],
                                             ),
                                           const SizedBox(width: 6),
                                           Text(
                                             'Ouvrir la pièce jointe',
                                             style: const TextStyle(
                                               
                                               fontWeight: FontWeight.w700,
                                               fontSize: 9,
                                               color: Colors.black,
                                             ),
                                           ),
                                         ],
                                       ),
                                     ),
                                   );
                                 },
                               ),
                             ],
                 ],
               ),
               

                 
               ),
             ),
             // Close button
             Positioned(
               right: -10,
               top: 0,
               child: GestureDetector(
                 onTap: () => Navigator.of(context).pop(),
                 child: Container(
                   width: 44,
                   height: 42,
                   decoration: BoxDecoration(
                     color: Colors.black.withValues(alpha: 0.8),
                     shape: BoxShape.circle,
                     border: Border.all(
                       color: Colors.white,
                       width: 1,
                     ),
                   ),
                   child: const Center(
                     child: Text(
                       'X',
                       style: TextStyle(
                         fontWeight: FontWeight.w400,
                         fontSize: 24,
                         color: Colors.white,
                       ),
                     ),
                   ),
                 ),
               ),
             ),
           ],
         ),
       ),
     );
  }
}
