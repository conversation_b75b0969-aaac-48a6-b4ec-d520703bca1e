
import 'package:kairos/core/device_info.dart';

class VerificationPinRequest extends DeviceInfo {
  final String nomComplet;
  final String pin;
  const VerificationPinRequest({
    required super.numeroTelephone,
    required super.marqueTelephone,
    required super.modelTelephone,
    required super.imeiTelephone,
    required super.numeroSerie,
    required this.nomComplet,
    required this.pin,
  });

  factory VerificationPinRequest.fromJson(Map<String, dynamic> json) {
    return VerificationPinRequest(
      numeroTelephone: json['numeroTelephone'],
      marqueTelephone: json['marqueTelephone'],
      modelTelephone: json['modelTelephone'],
      imeiTelephone: json['imeiTelephone'],
      numeroSerie: json['numeroSerie'],
      nomComplet: json['nomComplet'],
      pin: json['pin'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'nomComplet': nomComplet,
      'pin': pin,
    });
    return json;
  }
}
