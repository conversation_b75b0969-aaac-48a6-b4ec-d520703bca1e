

import 'package:kairos/core/device_info.dart';

class GetNewTokenRequest extends DeviceInfo {    
  
  const GetNewTokenRequest({
        required super.numeroTelephone,
        required super.marqueTelephone,
        required super.modelTelephone,
        required super.imeiTelephone,
        required super.numeroSerie,
    });

    factory GetNewTokenRequest.fromJson(Map<String, dynamic> json) {
        return GetNewTokenRequest(
            numeroTelephone: json['numeroTelephone'],
            marqueTelephone: json['marqueTelephone'],
            modelTelephone: json['modelTelephone'],
            imeiTelephone: json['imeiTelephone'],
            numeroSerie: json['numeroSerie'],
        );
    }
}
