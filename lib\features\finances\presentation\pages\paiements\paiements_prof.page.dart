import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/core/widgets/common/empty_message.widget.dart';
import 'package:kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:kairos/features/finances/presentation/bloc/payment_cubit.dart';
import 'package:kairos/features/finances/presentation/bloc/payment_state.dart';
import 'package:kairos/features/finances/presentation/pages/paiements/paiements_widgets/payment_item.widget.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:kairos/core/di/injection_container.dart';

class PaiementsProfPage extends StatefulWidget {
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? etudiant;

  const PaiementsProfPage({
    super.key,
    required this.school,
    this.etudiant,
  });

  @override
  State<PaiementsProfPage> createState() => _PaiementsProfPageState();
}

class _PaiementsProfPageState extends State<PaiementsProfPage> with SingleTickerProviderStateMixin {
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();
  late final AnimationController _animationController;
  bool _isSearchBarVisible = false;
  late TextEditingController _searchController;


  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _loadPaymentsData();
  }

  /// Load payments data
  Future<void> _loadPaymentsData() async {
    final phoneNumber = await _authLocalDataSource.getPhoneNumber();
    if (phoneNumber != null && mounted) {
      context.read<PaymentCubit>().loadProfessorPayments(
        codeEtab: widget.school.codeEtab,
        telephone: phoneNumber,
        codeUtilisateur: widget.school.codeUtilisateur,
      );
    }
  }

  /// Build payments list
  Widget _buildPaymentsList(List<dynamic> payments) {
    if (payments.isEmpty) {
      return const SliverFillRemaining(
        child: EmptyMessage(
          message: "Aucun paiement trouvé",
        ),
      );
    }

    return SliverPadding(
      padding: const EdgeInsets.only(top: 8.0, bottom: 16.0),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final payment = payments[index];
            return PaymentItem(
              payment: payment,
            );
          },
          childCount: payments.length,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Toggle search bar visibility
  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (!_isSearchBarVisible) {
        _animationController.reverse();
        _searchController.clear();
      } else {
        _animationController.forward();
      }
    });
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
            pageSection: HeaderEnum.finances,
            title: "PAIEMENTS",
            etablissementUtilisateur: widget.school,
            enfantDuTuteur: widget.etudiant,
            isSearchBarVisible: _isSearchBarVisible,
            onSearchTap: _toggleSearchBarVisibility,
          ),
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                pinned: true,
                delegate: SearchBarSliver(
                  extentHeight: _animationController.value * (_searchController.text.isNotEmpty ? 100.0 : 60.0),
                  searchController: _searchController,
                  onSearchChanged: (query) {},
                  onDateFilterChanged: null,
                  onClearDateFilter: () {},
                  hasActiveFilter: false,
                  hintText: "Rechercher paiements...",
                  startDate: null,
                  endDate: null,
                ),
              );
            },
          ),
          BlocBuilder<PaymentCubit, PaymentState>(
            builder: (context, state) {
              if (state is PaymentLoading) {
                return const SliverFillRemaining(
                  child: Center(
                    child: CustomSpinner(),
                  ),
                );
              }

              if (state is PaymentInitiatedError) {
                return SliverFillRemaining(
                  child: EmptyMessage(
                    message: "Erreur lors du chargement des paiements: ${state.message}",
                  ),
                );
              }

              if (state is ProfessorPaymentsLoaded) {
                return _buildPaymentsList(state.payments);
              }

              return const SliverFillRemaining(
                child: EmptyMessage(
                  message: "Aucun paiement disponible",
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
