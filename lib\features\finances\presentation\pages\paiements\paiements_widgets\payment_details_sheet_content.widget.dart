import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:kairos/core/theme/color_schemes.dart';
import 'package:kairos/features/finances/domain/entities/professor_payment_entity.dart';

/// Model for course details in payment dialog
class CourseDetail {
  final String courseName;
  final String hours;
  final double amount;

  CourseDetail({
    required this.courseName,
    required this.hours,
    required this.amount,
  });
}

class PaymentDetailsSheetContent extends StatelessWidget {
  final ProfessorPaymentEntity payment;
  final ScrollController scrollController; // To control scrolling within the sheet

  const PaymentDetailsSheetContent({
    super.key,
    required this.payment,
    required this.scrollController,
  });

  /// Generate mock course details based on payment
  List<CourseDetail> _generateMockCourses() {
    final baseAmount = payment.netAmount / 3;
    return [
      CourseDetail(
        courseName: 'Réseaux informatique',
        hours: '2H',
        amount: baseAmount,
      ),
      CourseDetail(
        courseName: 'Base de données',
        hours: '1.5H',
        amount: baseAmount * 0.8,
      ),
      CourseDetail(
        courseName: 'Programmation Web',
        hours: '3H',
        amount: baseAmount * 1.2,
      ),
    ];
  }

  /// Calculate mock total amount (net amount + deduction)
  double _getMockTotalAmount() {
    return payment.netAmount + 5000;
  }

  /// Get mock deduction amount
  double _getMockDeduction() {
    return 5000;
  }

  /// Get paid amount based on status
  double _getPaidAmount() {
    return payment.status.toLowerCase() == 'validé' ? payment.netAmount : 0;
  }

  @override
  Widget build(BuildContext context) {
    final formatter = NumberFormat('#,###', 'fr_FR');
    final courses = _generateMockCourses();
    final totalAmount = _getMockTotalAmount();
    final deduction = _getMockDeduction();
    final paidAmount = _getPaidAmount();

    return Container(
      // No need for Dialog, as it's a bottom sheet
      // constraints: const BoxConstraints(maxWidth: 400), // This might be handled by the sheet itself or parent
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    decoration: const BoxDecoration(
                      color: AppColorSchemes.secondaryGray,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: () => Navigator.of(context).pop(), // Use context from the builder
                      icon: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 20,
                      ),
                      padding: const EdgeInsets.all(4),
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                  ),
                ),
                Center(
                  child: Text(
                    'Détails du paiement',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Content
          Flexible(
            child: SingleChildScrollView(
              controller: scrollController, // Attach the scroll controller
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Summary Table
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.grey.shade300,
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          offset: const Offset(0, 2),
                          blurRadius: 4,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        _buildSummaryRow(
                          context,
                          'N° Opération',
                          payment.operationNumber,
                        ),
                        const SizedBox(height: 12),
                        _buildSummaryRow(
                          context,
                          'N° Décompte',
                          'DC-1-2025-8-862',
                        ),
                        const SizedBox(height: 12),
                        _buildSummaryRow(
                          context,
                          'Montant total décompté',
                          '${formatter.format(totalAmount.toInt())} XOF',
                        ),
                        const SizedBox(height: 12),
                        _buildSummaryRow(
                          context,
                          'Abattement',
                          '5.00 %',
                        ),
                        const SizedBox(height: 12),
                        _buildSummaryRow(
                          context,
                          'Net à payer',
                          '${formatter.format(payment.netAmount.toInt())} XOF',
                        ),
                        const SizedBox(height: 12),
                        _buildSummaryRow(
                          context,
                          'Montant payé',
                          '${formatter.format(paidAmount.toInt())} XOF',
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  // Courses Accordion
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ExpansionTile(
                      title: Text(
                        'Voir plus',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      backgroundColor: Colors.grey.shade200,
                      collapsedBackgroundColor: Colors.grey.shade200,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      collapsedShape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      children: [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(8),
                              bottomRight: Radius.circular(8),
                            ),
                          ),
                          child: Column(
                            children: courses.map((course) => _buildCourseItem(
                              context,
                              course,
                              formatter,
                            )).toList(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    String value,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildCourseItem(
    BuildContext context,
    CourseDetail course,
    NumberFormat formatter,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                course.courseName,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Text(
                '${course.hours} décompté',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'LPT1 | LICENCE 1 | LPT1 1A | SEM 1',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${formatter.format(course.amount.toInt())} à payer',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}