import 'package:kairos/core/widgets/common/snackbar_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:kairos/features/authentication/presentation/bloc/cubit/code_activation_cubit.dart'; // Correct import
import 'package:kairos/features/authentication/presentation/bloc/state/auth_state.dart'; // Correct import
import 'package:kairos/features/authentication/presentation/bloc/cubit/phone_authentication_cubit.dart'; // Correct import
import 'code_activation_error_dialog.widget.dart';
import 'package:flutter/gestures.dart';

class CodeActivationWidget extends StatefulWidget {
  const CodeActivationWidget({super.key, required this.pageController, required this.phoneNumber});
  final PageController pageController;
  final String phoneNumber;

  @override
  State<CodeActivationWidget> createState() => _CodeActivationWidgetState();
}

class _CodeActivationWidgetState extends State<CodeActivationWidget> {
  // Controllers for each PIN digit field
  final List<TextEditingController> _controllers = List.generate(4, (index) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(4, (index) => FocusNode());
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void dispose() {
    // Dispose all controllers and focus nodes
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  // Get the complete PIN code from all fields
  String get _pinCode {
    return _controllers.map((controller) => controller.text).join();
  }

  // Handle input change for auto-focus progression
  void _onChanged(String value, int index) {
    if (value.length == 1) {
      // Move to next field if current field has a digit
      if (index < 3) {
        _focusNodes[index + 1].requestFocus();
      } else {
        // Last field, unfocus
        _focusNodes[index].unfocus();
      }
    } else if (value.isEmpty) {
      // Move to previous field if current field is empty
      if (index > 0) {
        _focusNodes[index - 1].requestFocus();
      }
    } else if (value.length > 1) {
      // Handle paste functionality
      _handlePaste(value, index);
    }
  }

  // Handle paste functionality
  void _handlePaste(String pastedText, int startIndex) {
    // Extract only digits from pasted text
    String digits = pastedText.replaceAll(RegExp(r'[^0-9]'), '');

    if (digits.length >= 4) {
      // Distribute the first 4 digits across all fields
      for (int i = 0; i < 4; i++) {
        _controllers[i].text = digits[i];
      }
      // Focus on the last field
      _focusNodes[3].requestFocus();
    } else if (digits.isNotEmpty) {
      // Distribute available digits starting from current field
      for (int i = 0; i < digits.length && (startIndex + i) < 4; i++) {
        _controllers[startIndex + i].text = digits[i];
      }
      // Focus on the next appropriate field
      int nextFocus = (startIndex + digits.length - 1).clamp(0, 3);
      if (nextFocus < 3) {
        _focusNodes[nextFocus + 1].requestFocus();
      } else {
        _focusNodes[nextFocus].unfocus();
      }
    }
  }

  // Handle backspace key for better UX
  void _onKeyEvent(KeyEvent event, int index) {
    if (event is KeyDownEvent && event.logicalKey == LogicalKeyboardKey.backspace) {
      if (_controllers[index].text.isEmpty && index > 0) {
        // Move to previous field if current is empty and backspace is pressed
        _focusNodes[index - 1].requestFocus();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener( // Use MultiBlocListener to listen to both Cubits
      listeners: [
        BlocListener<PhoneAuthenticationCubit, AuthState>( // Listener for PhoneAuthenticationCubit (for resend SMS)
          listener: (phoneAuthContext, state) {
            if (state is ResendingAuthSms) {
               setState(() {
                _isLoading = true;
              });
            } else if (state is ResendAuthSmsSuccess) {
              // Show success message for resend
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(phoneAuthContext).showSnackBar(
                CustomSnackbar(
                  message: "Code d'activation renvoyé au numéro: ${widget.phoneNumber}",
                ).getSnackBar(),
              );
            } else if (state is ResendAuthSmsError) {
               setState(() {
                _isLoading = false;
              });
               ScaffoldMessenger.of(phoneAuthContext).showSnackBar(
                CustomSnackbar(
                  message: "Erreur lors du renvoi du code: ${state.message}",
                  isError: true,
                ).getSnackBar(),
              );
            } // End of ResendAuthSmsError
          }, // End of PhoneAuthenticationCubit listener
        ), // End of PhoneAuthenticationCubit BlocListener
        BlocListener<CodeActivationCubit, AuthState>( // Listener for CodeActivationCubit
          listener: (context, state) { // Use context directly
            if (state is CodeActivationLoadingState) {
              setState(() {
                _isLoading = true;
              });
            } else if (state is CodeActivationSuccessState) {
              setState(() {
                _isLoading = false;
              });
              // Navigate to the next page on success
              widget.pageController.nextPage(
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
              );
            } else if (state is CodeActivationErrorState) { // Handle error state
              setState(() {
                _isLoading = false;
              });
              showDialog(
                context: context, // Use context directly
                builder: (BuildContext dialogContext) {
                  return CodeActivationErrorDialog(
                    returnCode: state.returnCode,
                    userMessage: state.userMessage,
                    onResend: () {
                      context.read<PhoneAuthenticationCubit>().resendSms(widget.phoneNumber); // Use context directly
                    },
                  );
                },
              );
            }
          },
        ),
      ],
      child: Column( // Main widget tree
        mainAxisAlignment: MainAxisAlignment.spaceEvenly, // Keep original alignment
        children: [ // Keep original children structure
          Text( // Correct Text widget usage
            "CODE D'ACTIVATION",
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor, // Use context directly
            ),
          ),
          SvgPicture.asset("assets/images/logo_kairos.svg"), // Keep SvgPicture
          Divider( // Keep Divider
            color: Theme.of(context).primaryColor, // Use context directly
            thickness: 5,
            height: 20,
            indent: 100,
            endIndent: 100,
          ),
          const Spacer(), // Keep Spacer
          Flexible(flex: 8, child: SvgPicture.asset("assets/images/otp_code.svg")), // Keep Flexible and SvgPicture
          const Padding( // Keep Padding
            padding: EdgeInsets.symmetric(horizontal: 20.0), // Keep padding
            child: Text( // Correct Text widget usage
              "Veuillez saisir le code d'activation envoyé sur votre numéro de téléphone.",
              textAlign: TextAlign.center,
            ),
          ),
          const Spacer(flex: 3), // Keep Spacer
          Form(
            key: _formKey,
            child: SizedBox(
              width: 280,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(4, (index) {
                  return SizedBox(
                    width: 50,
                    child: KeyboardListener(
                      focusNode: FocusNode(),
                      onKeyEvent: (event) => _onKeyEvent(event, index),
                      child: TextFormField(
                        controller: _controllers[index],
                        focusNode: _focusNodes[index],
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                        decoration: const InputDecoration(
                          border: UnderlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey, width: 2),
                          ),
                          enabledBorder: UnderlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey, width: 2),
                          ),
                          focusedBorder: UnderlineInputBorder(
                            borderSide: BorderSide(color: Colors.blue, width: 2),
                          ),
                          errorBorder: UnderlineInputBorder(
                            borderSide: BorderSide(color: Colors.red, width: 2),
                          ),
                          focusedErrorBorder: UnderlineInputBorder(
                            borderSide: BorderSide(color: Colors.red, width: 2),
                          ),
                          contentPadding: EdgeInsets.symmetric(vertical: 8),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(1),
                        ],
                        onChanged: (value) => _onChanged(value, index),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return '';
                          }
                          return null;
                        },
                      ),
                    ),
                  );
                }),
              ),
            ),
          ),
          const Spacer(), // Keep Spacer
          FilledButton( // Keep FilledButton
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor), // Keep style
              fixedSize: WidgetStateProperty.all(Size(300, 50)),
            ),
            onPressed: _isLoading ? null : () async {
              debugPrint('the user clicked on `Continue` button');
              FocusScope.of(context).unfocus(); // Hide keyboard

              // Check if all 4 digits are entered
              if (_pinCode.length == 4) {
                setState(() {
                  _isLoading = true;
                });

                // Call verifyPin directly with the entered PIN
                context.read<CodeActivationCubit>().verifyPin(_pinCode, widget.phoneNumber);
              } else {
                // Show error if not all digits are entered
                ScaffoldMessenger.of(context).showSnackBar(
                  CustomSnackbar(
                    message: "Veuillez saisir le code d'activation complet (4 chiffres)",
                    isError: true,
                  ).getSnackBar(),
                );
              }
            },
            child: _isLoading // Keep loading indicator logic
                ? SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text( // Correct Text widget usage
                    "CONTINUER",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
          ),
          const Spacer(), // Keep Spacer
          Text.rich( // Correct Text.rich usage
            textAlign: TextAlign.center, // Keep textAlign
            TextSpan( // Correct TextSpan usage
              children: [ // Keep children
                const TextSpan(text: "Vous n'avez pas reçu le code d'activation?"), // Keep TextSpan
                TextSpan( // Keep TextSpan
                  text: "Renvoyer",
                  style: TextStyle(
                    color: Theme.of(context).primaryColor, // Use context directly
                    fontWeight: FontWeight.bold,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      context.read<PhoneAuthenticationCubit>().resendSms(widget.phoneNumber); // Use context directly
                    },
                ),
              ],
            ),
          ),
          const Spacer(flex: 3), // Keep Spacer
        ],
      ), // End of MultiBlocListener child
    ); // End of MultiBlocListener
  } // End of build method
} // End of _CodeActivationWidgetState class
