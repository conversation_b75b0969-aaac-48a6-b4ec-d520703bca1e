import 'package:dartz/dartz.dart';
import 'package:kairos/features/finances/data/models/payment_response_model.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';
import '../../domain/entities/payment_request_entity.dart';
import '../../domain/entities/payment_response_entity.dart';
import '../../domain/entities/professor_payment_entity.dart';
import '../../domain/repositories/payment_repository.dart';
import '../datasources/payment_remote_datasource.dart';
import '../models/payment_request_model.dart';

/// Implementation of PaymentRepository
class PaymentRepositoryImpl implements PaymentRepository {
  final PaymentRemoteDataSource remoteDataSource;

  PaymentRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, PaymentResponseEntity>> processPayment(
    PaymentRequestEntity paymentRequest,
  ) async {
    try {
      // Convert entity to model
      final paymentRequestModel = PaymentRequestModel.fromEntity(paymentRequest);
      
      // Make API call
      final PaymentResponseModel paymentResponseModel = await remoteDataSource.processPayment(paymentRequestModel);
      
      // Convert model to entity and return success
      return Right(PaymentResponseEntity.fromModel(paymentResponseModel));
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, List<ProfessorPaymentEntity>>> getProfessorPayments({
    required String codeEtab,
    required String telephone,
    required String codeUtilisateur,
  }) async {
    try {
      // Make API call to get professor payments
      final paymentModels = await remoteDataSource.getProfessorPayments(
        codeEtab: codeEtab,
        telephone: telephone,
        codeUtilisateur: codeUtilisateur,
      );

      // Convert models to entities
      final paymentEntities = paymentModels.map((model) => model.toEntity()).toList();

      return Right(paymentEntities);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: $e'));
    }
  }
}
