import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:kairos/core/api/api_client.dart';
import 'package:kairos/core/api/api_endpoints.dart';
import 'package:kairos/core/error/exceptions.dart';
import 'package:kairos/features/educational_resources/data/models/document_model.dart';
import 'package:kairos/features/educational_resources/data/models/resource_pedagogique_model.dart';
import 'package:kairos/features/student_records/data/models/dossier_attachment_model.dart';
import 'package:dio/dio.dart';
import 'package:kairos/core/constants/app_constants.dart';

/// Abstract class defining the contract for educational resources remote data source
abstract class RessourcesPedagogiquesRemoteDataSource {
  /// Fetches educational resources from the remote API
  Future<List<ResourcePedagogiqueModel>> getRessourcesPedagogiques({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Fetches educational resources filtered by date range from the remote API
  Future<List<ResourcePedagogiqueModel>> getRessourcesPedagogiquesFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? dateDebut,
    String? dateFin,
  });

  /// Fetches attachment for a specific educational resource
  Future<DossierAttachmentModel> getRessourcePedagogiqueAttachment({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required int idObject,
    String? codeUtilisateur,
  });
}

/// Implementation of RessourcesPedagogiquesRemoteDataSource
class RessourcesPedagogiquesRemoteDataSourceImpl implements RessourcesPedagogiquesRemoteDataSource {
  final ApiClient apiClient;

  RessourcesPedagogiquesRemoteDataSourceImpl({
    required this.apiClient,
  });

  @override
  Future<List<ResourcePedagogiqueModel>> getRessourcesPedagogiques({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    // If the user is a PRF (Enseignant), return mock data
    if (codeEtudiant.startsWith("P")) {
      // Mock data for PRF profile. Note: Structure should match ResourcePedagogiqueModel and ResourcePedagogiqueEntity.
      final mockResources = [
        ResourcePedagogiqueModel(
          idRessource: 1,
          description: "TD à rendre :Adressage_IP.pdf",
          professeur: "LPTI | Licence 1 | LPTI 1A | Semestre 2",
          classe: "", // Class is not directly available in this format for mock data
          dateAjout: "2024-08-01",
          documents: [
            DocumentModel(idDocument: 101, nomDocument: "Adressage_IP.pdf"),
          ],
        ),
        ResourcePedagogiqueModel(
          idRessource: 2,
          description: "TD à rendre : Nomenclature_IOT.pdf",
          professeur: "LPTI | Licence 1 | LPTI 1A | Semestre 2",
          classe: "",
          dateAjout: "2024-07-15",
          documents: [
            DocumentModel(idDocument: 102, nomDocument: "Nomenclature_IOT.pdf"),
          ],
        ),
        ResourcePedagogiqueModel(
          idRessource: 3,
          description: "Support de cours : Architecture_reseaux.pdf",
          professeur: "LPTI | Licence 1 | LPTI 1A | Semestre 2",
          classe: "",
          dateAjout: "2024-08-05",
          documents: [
            DocumentModel(idDocument: 103, nomDocument: "Architecture_reseaux.pdf"),
          ],
        ),
      ];
      debugPrint(' Returning mock data for PRF profile.');
      return mockResources;
    }

    // Original API call for other profiles
    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur for PAR profile if provided
      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      // Make HTTP GET request to the ressourcesPedagogiques endpoint
      final response = await apiClient.getWithToken(
        ApiEndpoints.ressourcesPedagogiques,
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes)
      );

      final decodedResponse = latin1.decode(response.data);
      final jsonResponse = jsonDecode(decodedResponse);

      // Debug print the raw response data
      debugPrint('RessourcesPedagogiquesRemoteDataSourceImpl: getRessourcesPedagogiques raw response data: $jsonResponse');

      // Parse response data
      if (jsonResponse is List) {
        return jsonResponse
            .map((json) => ResourcePedagogiqueModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Invalid response format: expected List but got ${jsonResponse.runtimeType}');
      }
    } on DioException catch (e) {
      debugPrint('RessourcesPedagogiquesRemoteDataSourceImpl: DioException: $e');
      debugPrint('RessourcesPedagogiquesRemoteDataSourceImpl: DioException response Status Code --> : ${e.response?.statusCode}');

      if (e.response != null) {
      final jsonResponse = jsonDecode(latin1.decode(e.response!.data));
      debugPrint('RessourcesPedagogiquesRemoteDataSourceImpl: DioException DAT -->: $jsonResponse');
        throw ServerException(jsonResponse['message']);
      } else if (e.response != null && e.response!.data is List) {
        throw ServerException(e.response!.data[0]);
      } else {
        throw ServerException('Erreur de format de réponse inattendue lors de la récupération des ressources pédagogiques. Veuillez réessayer plus tard.');
      }
    } catch (e) {
      debugPrint('RessourcesPedagogiquesRemoteDataSourceImpl: Exception: $e');
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la récupération des ressources pédagogiques: $e');
    }
  }

  @override
  Future<List<ResourcePedagogiqueModel>> getRessourcesPedagogiquesFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? dateDebut,
    String? dateFin,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur for PAR profile if provided
      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      // Add date filters if provided
      if (dateDebut != null && dateDebut.isNotEmpty) {
        queryParameters['dateDebut'] = dateDebut;
      }
      if (dateFin != null && dateFin.isNotEmpty) {
        queryParameters['dateFin'] = dateFin;
      }

      // Make HTTP GET request to the ressourcesPedagogiques endpoint with date filters
      final response = await apiClient.getWithToken(
        ApiEndpoints.ressourcesPedagogiques,
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes)
      );

      final decodedResponse = latin1.decode(response.data);
      final jsonResponse = jsonDecode(decodedResponse);

      // Debug print the raw response data
      debugPrint('RessourcesPedagogiquesRemoteDataSourceImpl: getRessourcesPedagogiquesFiltres raw response data: $jsonResponse');

      // Parse response data
      if (jsonResponse is List) {
        return jsonResponse
            .map((json) => ResourcePedagogiqueModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception('Invalid response format: expected List but got ${jsonResponse.runtimeType}');
      }
    } on DioException catch (e) {
      debugPrint('RessourcesPedagogiquesRemoteDataSourceImpl: DioException: $e');
      debugPrint('RessourcesPedagogiquesRemoteDataSourceImpl: DioException response: ${e.response}');
      debugPrint('RessourcesPedagogiquesRemoteDataSourceImpl: DioException DAT -->: ${e.response?.data}');
      final jsonResponse = jsonDecode(latin1.decode(e.response!.data));

      if (e.response != null && jsonResponse is Map<String, dynamic>) {
        throw ServerException(jsonResponse['message']);
      } else if (e.response != null && e.response!.data is List) {
        throw ServerException(e.response!.data[0]);
      } else {
        throw ServerException('Erreur de format de réponse inattendue lors de la récupération des ressources pédagogiques. Veuillez réessayer plus tard.');
      }
    } catch (e) {
      debugPrint('RessourcesPedagogiquesRemoteDataSourceImpl: Exception: $e');
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la récupération des ressources pédagogiques: $e');
    }
  }

  @override
  Future<DossierAttachmentModel> getRessourcePedagogiqueAttachment({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required int idObject,
    String? codeUtilisateur,
  }) async {
    try {
      Map<String, dynamic> queryParams = {
        'codeEtab': codeEtab,
        'numeroTel': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
        'idObject': idObject.toString(),
      };

      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParams['codeUtilisateur'] = codeUtilisateur;
      }

      final response = await apiClient.getWithToken(
        ApiEndpoints.documentRessource, // Reusing the same endpoint pattern as dossiers
        queryParameters: queryParams,
      );

      return DossierAttachmentModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(e.message ?? 'Server error occurred');
    } catch (e) {
      throw ServerException(AppConstants.unknownErrorMessage);
    }
  }
}
