import 'package:shared_preferences/shared_preferences.dart';

// Define keys for SharedPreferences
const String _prefsKeyPreviousTotalImpaye = 'previousTotalImpaye';
const String _prefsKeyPaymentAmount = 'paymentAmount';

/// Abstract class for local data source operations related to payments.
abstract class PaymentLocalDataSource {
  /// Saves payment tracking data (previous total unpaid amount and the current payment amount).
  ///
  /// Args:
  ///   previousTotalImpaye (double): The total unpaid amount before the current transaction.
  ///   paymentAmount (double): The amount of the current payment.
  Future<void> savePaymentTrackingData({required double previousTotalImpaye, required double paymentAmount});

  /// Retrieves the saved payment tracking data.
  ///
  /// Returns:
  ///   A Map containing 'previousTotalImpaye' and 'paymentAmount'.
  ///   Values will be null if not found.
  Future<Map<String, double?>> getPaymentTrackingData();

  /// Clears all saved payment tracking data.
  Future<void> clearPaymentTrackingData();
}

/// Implementation of PaymentLocalDataSource using SharedPreferences.
class PaymentLocalDataSourceImpl implements PaymentLocalDataSource {
  final SharedPreferences sharedPreferences;

  /// Constructs an instance of PaymentLocalDataSourceImpl.
  ///
  /// Args:
  ///   sharedPreferences (SharedPreferences): The SharedPreferences instance to use.
  PaymentLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<void> savePaymentTrackingData({required double previousTotalImpaye, required double paymentAmount}) async {
    await sharedPreferences.setDouble(_prefsKeyPreviousTotalImpaye, previousTotalImpaye);
    await sharedPreferences.setDouble(_prefsKeyPaymentAmount, paymentAmount);
  }

  @override
  Future<Map<String, double?>> getPaymentTrackingData() async {
    final previousTotalImpaye = sharedPreferences.getDouble(_prefsKeyPreviousTotalImpaye);
    final paymentAmount = sharedPreferences.getDouble(_prefsKeyPaymentAmount);
    return {
      _prefsKeyPreviousTotalImpaye: previousTotalImpaye,
      _prefsKeyPaymentAmount: paymentAmount,
    };
  }

  @override
  Future<void> clearPaymentTrackingData() async {
    await sharedPreferences.remove(_prefsKeyPreviousTotalImpaye);
    await sharedPreferences.remove(_prefsKeyPaymentAmount);
  }
}