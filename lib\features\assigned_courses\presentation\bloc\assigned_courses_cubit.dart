import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/error/failures.dart';
import '../../domain/usecases/get_assigned_courses_usecase.dart';
import 'assigned_courses_state.dart';

/// Cubit for managing assigned courses state
class AssignedCoursesCubit extends Cubit<AssignedCoursesState> {
  final GetAssignedCoursesUseCase getAssignedCoursesUseCase;

  AssignedCoursesCubit({
    required this.getAssignedCoursesUseCase,
  }) : super(const AssignedCoursesInitial());

  /// Load assigned courses data
  Future<void> loadAssignedCourses({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(const AssignedCoursesLoading());

    final params = GetAssignedCoursesParams(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
      codeUtilisateur: codeUtilisateur,
    );

    final failureOrAssignedCourses = await getAssignedCoursesUseCase(params);
    debugPrint('ASSIGNED COURSES CUBIT --> AssignedCourses: $failureOrAssignedCourses');

    failureOrAssignedCourses.fold(
      (failure) {
        emit(AssignedCoursesError(_mapFailureToMessage(failure)));
      },
      (assignedCourses) {
        debugPrint('ASSIGNED COURSES CUBIT --> AssignedCoursesLoaded State: $assignedCourses');
        emit(AssignedCoursesLoaded(
          assignedCourses: assignedCourses,
          filteredCourses: assignedCourses,
        ));
      },
    );
  }

  /// Filter courses based on search query
  void filterCourses(String query) {
    final currentState = state;
    if (currentState is AssignedCoursesLoaded) {
      if (query.isEmpty) {
        emit(currentState.copyWith(filteredCourses: currentState.assignedCourses));
      } else {
        final filteredCourses = currentState.assignedCourses
            .where((course) =>
                course.cours.toLowerCase().contains(query.toLowerCase()) ||
                course.niveau.toLowerCase().contains(query.toLowerCase()) ||
                course.semestre.toLowerCase().contains(query.toLowerCase()))
            .toList();
        emit(currentState.copyWith(filteredCourses: filteredCourses));
      }
    }
  }

  /// Refresh assigned courses data
  Future<void> refresh({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    await loadAssignedCourses(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
      codeUtilisateur: codeUtilisateur,
    );
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return 'Erreur du serveur lors du chargement des cours assignés';
      case NetworkFailure:
        return 'Erreur de connexion. Vérifiez votre connexion internet';
      case UnexpectedFailure:
        return 'Une erreur inattendue s\'est produite';
      default:
        return 'Erreur lors du chargement des cours assignés';
    }
  }
}
