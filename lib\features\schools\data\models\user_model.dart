import '../../../../core/device_info.dart';

/// Data model for user information that extends DeviceInfo
/// Represents the 'utilisateur' object in the API response
class UserModel extends DeviceInfo {
  final String id;
  final String nomComplet;

  const UserModel({
    required this.id,
    required this.nomComplet,
    required super.numeroTelephone,
    required super.marqueTelephone,
    required super.modelTelephone,
    required super.imeiTelephone,
    required super.numeroSerie,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      nomComplet: json['nomComplet'],
      numeroTelephone: json['numeroTelephone'] ?? '',
      marqueTelephone: json['marqueTelephone'],
      modelTelephone: json['modelTelephone'],
      imeiTelephone: json['imeiTelephone'],
      numeroSerie: json['numeroSerie'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'id': id,
      'nomComplet': nomComplet,
    });
    return json;
  }
}
