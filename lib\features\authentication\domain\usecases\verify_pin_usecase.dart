import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/auth_repository.dart';

/// Use case for verifying activation PIN
class VerifyPinUseCase {
  final AuthRepository repository;

  VerifyPinUseCase(this.repository);

  /// Execute the verify PIN use case
  ///
  /// [otp] - The entered OTP/PIN
  /// [phoneNumber] - The user's phone number
  /// Returns [Either<Failure, dynamic>] - Success or failure result
  Future<Either<Failure, dynamic>> call(String otp, String phoneNumber) async {
    return await repository.verifyPinWithDetails(otp, phoneNumber);
  }
}